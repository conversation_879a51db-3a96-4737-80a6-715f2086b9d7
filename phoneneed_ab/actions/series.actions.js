'use server';

import { connectToDatabase } from '@/lib/mongodb';
import { createSeriesSchema, updateSeriesSchema, seriesFilterSchema } from '@/schemas';
import { ObjectId } from 'mongodb';
import { revalidatePath } from 'next/cache';
import { generateSlug } from '@/lib/utils';
import {
  processFormData,
  convertFormFieldTypes,
  createSuccessResponse,
  createErrorResponse,
  toObjectIds
} from '@/lib/action-utils';

/**
 * Get all series with optional filtering
 */
export async function getSeries(filterData = {}) {
  try {
    const { db } = await connectToDatabase();

    // Parse and validate filter data
    const filter = seriesFilterSchema.parse(filterData);

    // Build query
    const query = {};
    if (filter.name) {
      query.name = { $regex: filter.name, $options: 'i' };
    }
    if (filter.brand_id && filter.brand_id.trim() !== '') {
      query.brand_id = filter.brand_id;
    }
    if (filter.is_active !== undefined) {
      query.is_active = filter.is_active;
    }

    // Calculate pagination
    const skip = (filter.page - 1) * filter.limit;

    // Execute query
    const seriesList = await db
      .collection('series')
      .find(query)
      .sort({ [filter.sort]: filter.order === 'asc' ? 1 : -1 })
      .skip(skip)
      .limit(filter.limit)
      .toArray();

    // Get total count for pagination
    const total = await db.collection('series').countDocuments(query);

    // Convert MongoDB objects to plain objects directly
    const serializedSeries = seriesList.map(series => ({
      _id: series._id.toString(),
      name: series.name,
      slug: series.slug,
      brand_id: series.brand_id,
      is_active: series.is_active,
      img: series.img,
      position: series.position,
      created_at: series.created_at,
      updated_at: series.updated_at
    }));

    // Return with pagination info
    return {
      series: serializedSeries,
      pagination: {
        total,
        page: filter.page,
        limit: filter.limit,
        pages: Math.ceil(total / filter.limit),
      },
    };
  } catch (error) {
    console.error('Error getting series:', error);
    throw new Error('Failed to get series');
  }
}

/**
 * Get a single series by ID
 */
export async function getSeriesById(id) {
  try {
    const { db } = await connectToDatabase();

    // Find the series by ID
    const series = await db.collection('series').findOne({
      _id: new ObjectId(id)
    });

    if (!series) {
      throw new Error('Series not found');
    }

    // Convert MongoDB object to plain object directly
    return {
      _id: series._id.toString(),
      name: series.name,
      slug: series.slug,
      brand_id: series.brand_id,
      is_active: series.is_active,
      img: series.img,
      position: series.position,
      created_at: series.created_at,
      updated_at: series.updated_at
    };
  } catch (error) {
    console.error('Error getting series:', error);
    throw new Error('Failed to get series');
  }
}

/**
 * Get series by brand ID
 */
export async function getSeriesByBrandId(brandId, includeInactive = false) {
  try {
    const { db } = await connectToDatabase();

    const query = { brand_id: brandId };
    if (!includeInactive) {
      query.is_active = true;
    }

    const seriesList = await db
      .collection('series')
      .find(query)
      .sort({ position: 1 })
      .toArray();

    // Convert MongoDB objects to plain objects directly
    return seriesList.map(series => ({
      _id: series._id.toString(),
      name: series.name,
      slug: series.slug,
      brand_id: series.brand_id,
      is_active: series.is_active,
      img: series.img,
      position: series.position,
      created_at: series.created_at,
      updated_at: series.updated_at
    }));
  } catch (error) {
    console.error('Error getting series by brand:', error);
    throw new Error('Failed to get series');
  }
}

/**
 * Create a new series
 */
export async function createSeries(formData) {
  try {
    // Process and convert form data
    let seriesData = await processFormData(formData);
    seriesData = await convertFormFieldTypes(seriesData);

    const { db } = await connectToDatabase();

    // Parse and validate form data
    const validatedData = createSeriesSchema.parse(seriesData);

    // Generate slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = generateSlug(validatedData.name);
    }

    // Verify brand exists
    const brand = await db.collection('brands').findOne({ _id: new ObjectId(validatedData.brand_id) });
    if (!brand) {
      return createErrorResponse('Brand not found');
    }

    // Check if slug already exists
    const existingSeries = await db.collection('series').findOne({
      slug: validatedData.slug,
      brand_id: validatedData.brand_id
    });

    if (existingSeries) {
      return createErrorResponse(
        `A series with the slug "${validatedData.slug}" already exists for this brand. Please use a different name or slug.`
      );
    }

    // Add timestamps
    const now = new Date();
    const seriesToInsert = {
      ...validatedData,
      created_at: now,
      updated_at: now,
    };

    // Insert series
    const result = await db.collection('series').insertOne(seriesToInsert);

    // Revalidate related paths
    revalidatePath('/dashboard/series');
    revalidatePath(`/dashboard/brands/${validatedData.brand_id}`);

    // Return success with redirect info
    return createSuccessResponse(
      'Series created successfully',
      true,
      `/dashboard/series/${result.insertedId.toString()}`
    );
  } catch (error) {
    return createErrorResponse('Failed to create series', error);
  }
}

/**
 * Update an existing series
 */
export async function updateSeries(id, formData) {
  try {
    // Process and convert form data
    let seriesData = await processFormData(formData);
    seriesData = await convertFormFieldTypes(seriesData);

    const { db } = await connectToDatabase();

    // Parse and validate form data
    const validatedData = updateSeriesSchema.parse(seriesData);

    // Remove _id from update data if present
    const { _id, ...updateData } = validatedData;

    // Get the current series to check brand_id
    const currentSeries = await db.collection('series').findOne({ _id: new ObjectId(id) });
    if (!currentSeries) {
      return createErrorResponse('Series not found');
    }

    // Check if slug is being updated
    if (updateData.slug) {
      // Check if slug already exists for a different series in the same brand
      const existingSeries = await db.collection('series').findOne({
        slug: updateData.slug,
        brand_id: updateData.brand_id || currentSeries.brand_id,
        _id: { $ne: new ObjectId(id) } // Exclude the current series
      });

      if (existingSeries) {
        return createErrorResponse(
          `A series with the slug "${updateData.slug}" already exists for this brand. Please use a different slug.`
        );
      }
    }

    // Add updated timestamp
    updateData.updated_at = new Date();

    // Update series
    const result = await db.collection('series').updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );

    if (result.matchedCount === 0) {
      return createErrorResponse('Series not found');
    }

    // Revalidate related paths
    revalidatePath('/dashboard/series');
    revalidatePath(`/dashboard/series/${id}`);
    revalidatePath(`/dashboard/brands/${currentSeries.brand_id}`);

    // If brand_id was updated, revalidate the new brand page too
    if (updateData.brand_id && updateData.brand_id !== currentSeries.brand_id) {
      revalidatePath(`/dashboard/brands/${updateData.brand_id}`);
    }

    // Return success with redirect info
    return createSuccessResponse(
      'Series updated successfully',
      true,
      `/dashboard/series/${id}`
    );
  } catch (error) {
    return createErrorResponse('Failed to update series', error);
  }
}

/**
 * Delete a series
 */
export async function deleteSeries(id) {
  try {
    const { db } = await connectToDatabase();

    // Get the series to get brand_id for revalidation
    const series = await db.collection('series').findOne({ _id: new ObjectId(id) });

    if (!series) {
      return createErrorResponse('Series not found');
    }

    // Check if series is used in any devices
    const devicesCount = await db.collection('devices').countDocuments({ series_id: id });

    if (devicesCount > 0) {
      return createErrorResponse('Cannot delete series because it is used in devices');
    }

    // Delete series
    await db.collection('series').deleteOne({ _id: new ObjectId(id) });

    // Revalidate related paths
    revalidatePath('/dashboard/series');
    revalidatePath(`/dashboard/brands/${series.brand_id}`);

    return createSuccessResponse('Series deleted successfully');
  } catch (error) {
    return createErrorResponse('Failed to delete series', error);
  }
}

/**
 * Bulk delete series
 * @param {string[]} ids - Array of series IDs to delete
 */
export async function bulkDeleteSeries(ids) {
  try {
    const { db } = await connectToDatabase();

    // Convert string IDs to ObjectId
    const objectIds = toObjectIds(ids);

    // Check if any series are used in devices
    const devicesCount = await db.collection('devices').countDocuments({
      series_id: { $in: ids }
    });

    if (devicesCount > 0) {
      return createErrorResponse('Cannot delete some series because they are used in devices');
    }

    // Get the series to get brand_ids for revalidation
    const seriesToDelete = await db.collection('series')
      .find({ _id: { $in: objectIds } })
      .toArray();

    // Extract unique brand IDs
    const brandIds = [...new Set(seriesToDelete.map(series => series.brand_id))];

    // Delete series
    const result = await db.collection('series').deleteMany({
      _id: { $in: objectIds }
    });

    // Revalidate related paths
    revalidatePath('/dashboard/series');

    // Revalidate each affected brand
    brandIds.forEach(brandId => {
      revalidatePath(`/dashboard/brands/${brandId}`);
    });

    return createSuccessResponse(
      `${result.deletedCount} series deleted successfully`,
      false,
      null,
      { deletedCount: result.deletedCount }
    );
  } catch (error) {
    return createErrorResponse('Failed to delete series', error);
  }
}

/**
 * Get the next available position for a series in a brand
 */
export async function getNextSeriesPosition(brandId) {
  try {
    const { db } = await connectToDatabase();

    // Find the series with the highest position in this brand
    const highestPositionSeries = await db.collection('series')
      .find({ brand_id: brandId })
      .sort({ position: -1 })
      .limit(1)
      .toArray();

    // If no series exist for this brand, start with position 1
    if (!highestPositionSeries.length) {
      return createSuccessResponse('No existing series found', false, null, { nextPosition: 1 });
    }

    // Return the next position (highest + 1)
    const nextPosition = highestPositionSeries[0].position + 1;

    return createSuccessResponse('Next position retrieved', false, null, { nextPosition });
  } catch (error) {
    return createErrorResponse('Failed to get next position', error);
  }
}
