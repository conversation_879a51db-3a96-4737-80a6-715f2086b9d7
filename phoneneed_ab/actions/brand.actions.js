'use server';

import { connectToDatabase } from '@/lib/mongodb';
import { createBrandSchema, updateBrandSchema, brandFilterSchema } from '@/schemas';
import { ObjectId } from 'mongodb';
import { revalidatePath } from 'next/cache';
import { generateSlug } from '@/lib/utils';
import {
  processFormData,
  convertFormFieldTypes,
  createErrorResponse,
  createPaginationResult
} from '@/lib/action-utils';

// Import GSMArena API
const gsmarena = require('gsmarena-api');

/**
 * Get all brands with optional filtering
 */
export async function getBrands(filterData = {}) {
  try {
    const { db } = await connectToDatabase();

    // Parse and validate filter data
    const filter = brandFilterSchema.parse(filterData);

    // Build query
    const query = {};
    if (filter.name) {
      query.name = { $regex: filter.name, $options: 'i' };
    }
    if (filter.is_active !== undefined) {
      query.is_active = filter.is_active;
    }

    // Calculate pagination
    const skip = (filter.page - 1) * filter.limit;

    // Execute query
    const brands = await db
      .collection('brands')
      .find(query)
      .sort({ [filter.sort]: filter.order === 'asc' ? 1 : -1 })
      .skip(skip)
      .limit(filter.limit)
      .toArray();

    // Get total count for pagination
    const total = await db.collection('brands').countDocuments(query);

    // Create pagination result
    const pagination = createPaginationResult(total, filter.page, filter.limit);

    // Convert MongoDB objects to plain objects
    const serializedBrands = brands.map(brand => ({
      _id: brand._id.toString(),
      name: brand.name,
      slug: brand.slug,
      is_active: brand.is_active,
      img: brand.img,
      position: brand.position,
      created_at: brand.created_at,
      updated_at: brand.updated_at
    }));

    return {
      brands: serializedBrands,
      pagination,
    };
  } catch (error) {
    console.error('Error getting brands:', error);
    throw new Error('Failed to get brands');
  }
}

/**
 * Get a single brand by ID
 */
export async function getBrandById(id) {
  try {
    const { db } = await connectToDatabase();

    // Find the brand by ID
    const brand = await db.collection('brands').findOne({ _id: new ObjectId(id) });

    if (!brand) {
      throw new Error('Brand not found');
    }

    // Convert MongoDB object to plain object
    return {
      _id: brand._id.toString(),
      name: brand.name,
      slug: brand.slug,
      is_active: brand.is_active,
      img: brand.img,
      position: brand.position,
      external_data: brand.external_data || {
        source: 'manual',
        external_id: '',
        last_synced: null,
      },
      created_at: brand.created_at,
      updated_at: brand.updated_at
    };
  } catch (error) {
    console.error('Error getting brand:', error);
    throw new Error('Failed to get brand');
  }
}

/**
 * Create a new brand
 */
export async function createBrand(formData) {
  try {
    const { db } = await connectToDatabase();

    // Process form data
    const brandData = await processFormData(formData);

    // Convert field types
    const processedData = await convertFormFieldTypes(brandData);

    // Parse and validate form data
    const validatedData = createBrandSchema.parse(processedData);

    // Generate slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = generateSlug(validatedData.name);
    }

    // Check if slug already exists
    const existingBrand = await db.collection('brands').findOne({ slug: validatedData.slug });
    if (existingBrand) {
      return {
        success: false,
        message: `A brand with the slug "${validatedData.slug}" already exists. Please use a different name or slug.`
      };
    }

    // Add timestamps
    const now = new Date();
    const brandToInsert = {
      ...validatedData,
      created_at: now,
      updated_at: now,
    };

    // Insert brand
    const result = await db.collection('brands').insertOne(brandToInsert);

    // Revalidate related paths
    revalidatePath('/dashboard/brands');

    // Return success with redirect info
    return {
      success: true,
      message: 'Brand created successfully',
      redirect: true,
      redirectUrl: `/dashboard/brands/${result.insertedId.toString()}`
    };
  } catch (error) {
    console.error('Error creating brand:', error);
    console.error('Error stack:', error.stack);

    // If it's a ZodError, log the specific validation issues
    if (error.name === 'ZodError' && error.issues) {
      console.error('Validation errors:', JSON.stringify(error.issues, null, 2));
    }

    return {
      success: false,
      message: error.message || 'Failed to create brand'
    };
  }
}

/**
 * Update an existing brand
 */
export async function updateBrand(id, formData) {
  try {
    const { db } = await connectToDatabase();

    // Process form data
    const brandData = await processFormData(formData);

    // Convert field types
    const processedData = await convertFormFieldTypes(brandData);

    // Parse and validate form data
    const validatedData = updateBrandSchema.parse(processedData);

    // Remove _id from update data if present
    const { _id, ...updateData } = validatedData;

    // Check if slug is being updated
    if (updateData.slug) {
      // Check if slug already exists for a different brand
      const existingBrand = await db.collection('brands').findOne({
        slug: updateData.slug,
        _id: { $ne: new ObjectId(id) } // Exclude the current brand
      });

      if (existingBrand) {
        return {
          success: false,
          message: `A brand with the slug "${updateData.slug}" already exists. Please use a different slug.`
        };
      }
    }

    // Add updated timestamp
    updateData.updated_at = new Date();

    // Update brand
    const result = await db.collection('brands').updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: 'Brand not found'
      };
    }

    // Revalidate related paths
    revalidatePath('/dashboard/brands');
    revalidatePath(`/dashboard/brands/${id}`);

    // Return success with redirect info
    return {
      success: true,
      message: 'Brand updated successfully',
      redirect: true,
      redirectUrl: `/dashboard/brands/${id}`
    };
  } catch (error) {
    console.error('Error updating brand:', error);
    return {
      success: false,
      message: error.message || 'Failed to update brand'
    };
  }
}

/**
 * Delete a brand
 */
export async function deleteBrand(id) {
  try {
    const { db } = await connectToDatabase();

    // Check if brand is used in any series
    const seriesCount = await db.collection('series').countDocuments({ brand_id: id });

    if (seriesCount > 0) {
      return {
        success: false,
        message: 'Cannot delete brand because it is used in series'
      };
    }

    // Delete brand
    const result = await db.collection('brands').deleteOne({ _id: new ObjectId(id) });

    if (result.deletedCount === 0) {
      return {
        success: false,
        message: 'Brand not found'
      };
    }

    // Revalidate related paths
    revalidatePath('/dashboard/brands');

    return {
      success: true,
      message: 'Brand deleted successfully'
    };
  } catch (error) {
    console.error('Error deleting brand:', error);
    return {
      success: false,
      message: error.message || 'Failed to delete brand'
    };
  }
}

/**
 * Bulk delete brands
 * @param {string[]} ids - Array of brand IDs to delete
 */
export async function bulkDeleteBrands(ids) {
  try {
    const { db } = await connectToDatabase();

    // Convert string IDs to ObjectId
    const objectIds = ids.map(id => new ObjectId(id));

    // Check if any brands are used in series
    const seriesCount = await db.collection('series').countDocuments({
      brand_id: { $in: ids }
    });

    if (seriesCount > 0) {
      return {
        success: false,
        message: 'Cannot delete some brands because they are used in series'
      };
    }

    // Delete brands
    const result = await db.collection('brands').deleteMany({
      _id: { $in: objectIds }
    });

    // Revalidate related paths
    revalidatePath('/dashboard/brands');

    return {
      success: true,
      message: `${result.deletedCount} brands deleted successfully`,
      data: { deletedCount: result.deletedCount }
    };
  } catch (error) {
    console.error('Error bulk deleting brands:', error);
    return {
      success: false,
      message: error.message || 'Failed to delete brands'
    };
  }
}

/**
 * Get the next available position for a brand
 */
export async function getNextBrandPosition() {
  try {
    const { db } = await connectToDatabase();

    // Find the brand with the highest position
    const highestPositionBrand = await db.collection('brands')
      .find()
      .sort({ position: -1 })
      .limit(1)
      .toArray();

    // If no brands exist, start with position 1
    if (!highestPositionBrand.length) {
      return 1;
    }

    // Return the next position (highest + 1)
    return highestPositionBrand[0].position + 1;
  } catch (error) {
    console.error('Error getting next brand position:', error);
    return 1; // Default to 1 in case of error
  }
}

/**
 * Get available brands from GSMArena with import status
 */
export async function getAvailableBrandsFromGSMArena() {
  try {
    const { db } = await connectToDatabase();

    // Fetch brands from GSMArena
    const gsmarenaBrands = await gsmarena.catalog.getBrands();

    if (!gsmarenaBrands || gsmarenaBrands.length === 0) {
      return await createErrorResponse('No brands found from GSMArena');
    }

    // Get all existing brands with GSMArena external_ids
    const existingBrands = await db.collection('brands').find({
      'external_data.source': 'gsmarena'
    }).toArray();

    // Create a map of existing GSMArena IDs
    const existingGSMArenaIds = new Set(
      existingBrands.map(brand => brand.external_data.external_id)
    );

    // Map GSMArena brands with import status
    const brandsWithStatus = gsmarenaBrands.map(gsmarenaBrand => ({
      id: gsmarenaBrand.id,
      name: gsmarenaBrand.name,
      devices: gsmarenaBrand.devices,
      isImported: existingGSMArenaIds.has(gsmarenaBrand.id),
      slug: generateSlug(gsmarenaBrand.name)
    }));

    const responseData = {
      brands: brandsWithStatus,
      total: gsmarenaBrands.length,
      imported: existingGSMArenaIds.size,
      available: gsmarenaBrands.length - existingGSMArenaIds.size
    };

    return {
      success: true,
      message: 'Brands fetched successfully',
      data: responseData
    };

  } catch (error) {
    console.error('Error fetching brands from GSMArena:', error);
    return await createErrorResponse(`Failed to fetch brands: ${error.message}`);
  }
}

/**
 * Import selected brands from GSMArena
 */
export async function importSelectedBrands(selectedBrandIds) {
  try {
    const { db } = await connectToDatabase();

    if (!selectedBrandIds || selectedBrandIds.length === 0) {
      return {
        success: false,
        message: 'No brands selected for import'
      };
    }

    // Fetch all brands from GSMArena
    const gsmarenaBrands = await gsmarena.catalog.getBrands();

    if (!gsmarenaBrands || gsmarenaBrands.length === 0) {
      return {
        success: false,
        message: 'No brands found from GSMArena'
      };
    }

    // Filter only the selected brands
    const selectedBrands = gsmarenaBrands.filter(brand =>
      selectedBrandIds.includes(brand.id)
    );

    if (selectedBrands.length === 0) {
      return {
        success: false,
        message: 'Selected brands not found in GSMArena data'
      };
    }

    let createdCount = 0;
    let updatedCount = 0;
    let skippedCount = 0;
    const errors = [];

    // Get the current highest position from database
    let currentPosition = await getNextBrandPosition();

    for (const gsmarenaBrand of selectedBrands) {
      try {
        const slug = generateSlug(gsmarenaBrand.name);

        // Check if brand already exists (by external_id or slug)
        const existingBrand = await db.collection('brands').findOne({
          $or: [
            { 'external_data.external_id': gsmarenaBrand.id },
            { slug: slug }
          ]
        });

        const now = new Date();

        if (existingBrand) {
          // Update existing brand
          const updateData = {
            name: gsmarenaBrand.name,
            external_data: {
              source: 'gsmarena',
              external_id: gsmarenaBrand.id,
              last_synced: now,
            },
            updated_at: now
          };

          await db.collection('brands').updateOne(
            { _id: existingBrand._id },
            { $set: updateData }
          );

          updatedCount++;
        } else {
          // Create new brand
          const brandData = {
            name: gsmarenaBrand.name,
            slug: slug,
            is_active: true,
            img: '', // GSMArena doesn't provide brand images in the brands list
            position: currentPosition,
            external_data: {
              source: 'gsmarena',
              external_id: gsmarenaBrand.id,
              last_synced: now,
            },
            created_at: now,
            updated_at: now,
          };

          // Validate the data
          const validatedData = createBrandSchema.parse(brandData);

          await db.collection('brands').insertOne(validatedData);

          createdCount++;
          currentPosition++;
        }
      } catch (error) {
        console.error(`Error processing brand ${gsmarenaBrand.name}:`, error);
        errors.push(`${gsmarenaBrand.name}: ${error.message}`);
        skippedCount++;
      }
    }

    // Revalidate related paths
    revalidatePath('/dashboard/brands');

    const summary = {
      total: selectedBrands.length,
      created: createdCount,
      updated: updatedCount,
      skipped: skippedCount,
      errors: errors
    };



    return {
      success: true,
      message: `Import completed: ${createdCount} created, ${updatedCount} updated, ${skippedCount} skipped`,
      data: summary
    };

  } catch (error) {
    console.error('Error importing selected brands from GSMArena:', error);
    return {
      success: false,
      message: `Failed to import brands: ${error.message}`
    };
  }
}

/**
 * Get brand by GSMArena ID
 */
export async function getBrandByGSMArenaId(gsmarenaId) {
  try {
    const { db } = await connectToDatabase();

    const brand = await db.collection('brands').findOne({
      'external_data.external_id': gsmarenaId
    });

    if (!brand) {
      return null;
    }

    return {
      _id: brand._id.toString(),
      name: brand.name,
      slug: brand.slug,
      is_active: brand.is_active,
      img: brand.img,
      position: brand.position,
      external_data: brand.external_data,
      created_at: brand.created_at,
      updated_at: brand.updated_at
    };
  } catch (error) {
    console.error('Error getting brand by GSMArena ID:', error);
    throw new Error('Failed to get brand by GSMArena ID');
  }
}
