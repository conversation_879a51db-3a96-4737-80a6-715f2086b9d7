'use server';

import { connectToDatabase } from '@/lib/mongodb';
import { createCategorySchema, updateCategorySchema, categoryFilterSchema } from '@/schemas';
import { ObjectId } from 'mongodb';
import { revalidatePath } from 'next/cache';
import { generateSlug } from '@/lib/utils';
import {
  processFormData,
  convertFormFieldTypes,
  createSuccessResponse,
  createErrorResponse,
  toObjectId,
  toObjectIds
} from '@/lib/action-utils';

/**
 * Get all categories with optional filtering
 */
export async function getCategories(filterData = {}) {
  try {
    const { db } = await connectToDatabase();

    // Parse and validate filter data
    const filter = categoryFilterSchema.parse(filterData);

    // Build query
    const query = {};
    if (filter.name) {
      query.name = { $regex: filter.name, $options: 'i' };
    }
    if (filter.parent_id !== undefined) {
      if (filter.parent_id === 'none' || filter.parent_id === null) {
        query.parent_id = null;
      } else {
        query.parent_id = filter.parent_id;
      }
    }
    if (filter.is_active !== undefined) {
      query.is_active = filter.is_active;
    }
    if (filter.is_primary !== undefined) {
      query.is_primary = filter.is_primary;
    }
    if (filter.is_menu_item !== undefined) {
      query.is_menu_item = filter.is_menu_item;
    }

    // Calculate pagination
    const skip = (filter.page - 1) * filter.limit;

    // Execute query
    const categories = await db
      .collection('categories')
      .find(query)
      .sort({ [filter.sort]: filter.order === 'asc' ? 1 : -1 })
      .skip(skip)
      .limit(filter.limit)
      .toArray();

    // Get total count for pagination
    const total = await db.collection('categories').countDocuments(query);

    // Convert MongoDB objects to plain objects
    const serializedCategories = categories.map(category => ({
      ...category,
      _id: category._id.toString(),
      parent_id: category.parent_id ? category.parent_id.toString() : null,
    }));

    return {
      categories: serializedCategories,
      pagination: {
        total,
        page: filter.page,
        limit: filter.limit,
        pages: Math.ceil(total / filter.limit),
      },
    };
  } catch (error) {
    console.error('Error getting categories:', error);
    throw new Error('Failed to get categories');
  }
}

/**
 * Get a category tree structure
 * Returns categories in a hierarchical structure
 */
export async function getCategoryTree() {
  try {
    const { db } = await connectToDatabase();

    // Get all categories
    const categories = await db
      .collection('categories')
      .find({})
      .sort({ position: 1 })
      .toArray();

    // Convert MongoDB objects to plain objects
    const serializedCategories = categories.map(category => ({
      ...category,
      _id: category._id.toString(),
      parent_id: category.parent_id ? category.parent_id.toString() : null,
    }));

    // Build tree structure
    const categoryMap = {};
    const rootCategories = [];

    // First pass: create a map of categories by ID
    serializedCategories.forEach(category => {
      categoryMap[category._id] = {
        ...category,
        children: []
      };
    });

    // Second pass: build the tree structure
    serializedCategories.forEach(category => {
      if (category.parent_id) {
        // Add to parent's children if parent exists
        if (categoryMap[category.parent_id]) {
          categoryMap[category.parent_id].children.push(categoryMap[category._id]);
        } else {
          // If parent doesn't exist, add to root
          rootCategories.push(categoryMap[category._id]);
        }
      } else {
        // Add to root if no parent
        rootCategories.push(categoryMap[category._id]);
      }
    });

    return rootCategories;
  } catch (error) {
    console.error('Error getting category tree:', error);
    throw new Error('Failed to get category tree');
  }
}

/**
 * Get a single category by ID
 */
export async function getCategoryById(id) {
  try {
    const { db } = await connectToDatabase();

    // Find the category by ID
    const category = await db.collection('categories').findOne({ _id: new ObjectId(id) });

    if (!category) {
      throw new Error('Category not found');
    }

    // Convert MongoDB object to plain object
    return {
      ...category,
      _id: category._id.toString(),
      parent_id: category.parent_id ? category.parent_id.toString() : null,
    };
  } catch (error) {
    console.error('Error getting category:', error);
    throw new Error('Failed to get category');
  }
}

/**
 * Create a new category
 */
export async function createCategory(formData) {
  try {
    const { db } = await connectToDatabase();

    // Process form data
    const categoryData = await processFormData(formData);

    // Convert field types
    const processedData = await convertFormFieldTypes(categoryData);

    // Parse and validate form data
    const validatedData = createCategorySchema.parse(processedData);

    // Generate slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = generateSlug(validatedData.name);
    }

    // Check if slug already exists
    const existingCategory = await db.collection('categories').findOne({ slug: validatedData.slug });
    if (existingCategory) {
      return createErrorResponse(`A category with the slug "${validatedData.slug}" already exists. Please use a different name or slug.`);
    }

    // Handle parent_id
    let parentId = null;
    let level = 1;
    let path = '/';
    let pathSlugs = [];

    if (validatedData.parent_id && validatedData.parent_id !== 'none') {
      // Convert to ObjectId
      parentId = new ObjectId(validatedData.parent_id);

      // Get parent category to determine level and path
      const parentCategory = await db.collection('categories').findOne({ _id: parentId });
      if (!parentCategory) {
        return createErrorResponse('Parent category not found');
      }

      level = parentCategory.level + 1;
      path = `${parentCategory.path}${parentCategory._id.toString()}/`;

      // Create path_slugs array by copying parent's path_slugs and adding parent's slug
      pathSlugs = [...(parentCategory.path_slugs || []), parentCategory.slug];
    }

    // Get next position
    const positionQuery = { parent_id: parentId };
    const lastCategory = await db
      .collection('categories')
      .find(positionQuery)
      .sort({ position: -1 })
      .limit(1)
      .toArray();

    // Start positions from 0 to be consistent with moveCategory function
    const position = lastCategory.length > 0 ? lastCategory[0].position + 1 : 0;

    // Add timestamps and additional fields
    const now = new Date();
    const categoryToInsert = {
      ...validatedData,
      parent_id: parentId,
      level,
      path,
      path_slugs: pathSlugs,
      position,
      children: [],
      created_at: now,
      updated_at: now,
    };

    // Insert category
    const result = await db.collection('categories').insertOne(categoryToInsert);

    // If this category has a parent, update the parent's children array
    if (parentId) {
      await db.collection('categories').updateOne(
        { _id: parentId },
        { $push: { children: result.insertedId.toString() } }
      );
    }

    // Revalidate related paths
    revalidatePath('/dashboard/categories');

    // Return success with redirect info
    return createSuccessResponse('Category created successfully', true, `/dashboard/categories/${result.insertedId.toString()}`);
  } catch (error) {
    console.error('Error creating category:', error);
    return createErrorResponse('Failed to create category', error);
  }
}

/**
 * Update an existing category
 */
export async function updateCategory(id, formData) {
  try {
    const { db } = await connectToDatabase();

    // Process form data
    const categoryData = await processFormData(formData);

    // Convert field types
    const processedData = await convertFormFieldTypes(categoryData);

    // Parse and validate form data
    const validatedData = updateCategorySchema.parse(processedData);

    // Remove _id from update data if present
    const { _id, ...updateData } = validatedData;

    // Get the current category
    const currentCategory = await db.collection('categories').findOne({ _id: new ObjectId(id) });
    if (!currentCategory) {
      return createErrorResponse('Category not found');
    }

    // Check if slug is being updated
    if (updateData.slug && updateData.slug !== currentCategory.slug) {
      // Check if slug already exists for a different category
      const existingCategory = await db.collection('categories').findOne({
        slug: updateData.slug,
        _id: { $ne: new ObjectId(id) }
      });

      if (existingCategory) {
        return createErrorResponse(`A category with the slug "${updateData.slug}" already exists. Please use a different slug.`);
      }
    }

    // Handle parent_id changes
    let parentId = currentCategory.parent_id;
    let level = currentCategory.level;
    let path = currentCategory.path;
    let pathSlugs = currentCategory.path_slugs || [];
    let needsChildrenUpdate = false;

    if (updateData.parent_id !== undefined) {
      if (updateData.parent_id === 'none' || updateData.parent_id === null) {
        // Moving to root level
        parentId = null;
        level = 1;
        path = '/';
        pathSlugs = [];
        needsChildrenUpdate = true;
      } else if (updateData.parent_id !== (currentCategory.parent_id ? currentCategory.parent_id.toString() : null)) {
        // Moving to a different parent
        parentId = new ObjectId(updateData.parent_id);

        // Check if new parent exists
        const newParent = await db.collection('categories').findOne({ _id: parentId });
        if (!newParent) {
          return createErrorResponse('Parent category not found');
        }

        // Check for circular reference
        if (newParent.path.includes(`/${id}/`)) {
          return createErrorResponse('Cannot move a category to its own descendant');
        }

        level = newParent.level + 1;
        path = `${newParent.path}${newParent._id.toString()}/`;
        pathSlugs = [...(newParent.path_slugs || []), newParent.slug];
        needsChildrenUpdate = true;
      }
    }

    // Add updated timestamp and fields
    const finalUpdateData = {
      ...updateData,
      parent_id: parentId,
      level,
      path,
      path_slugs: pathSlugs,
      updated_at: new Date()
    };

    // Update category
    const result = await db.collection('categories').updateOne(
      { _id: new ObjectId(id) },
      { $set: finalUpdateData }
    );

    if (result.matchedCount === 0) {
      return createErrorResponse('Category not found');
    }

    // If parent changed, update all children's level and path
    if (needsChildrenUpdate) {
      await updateChildrenPaths(db, id, path, level);

      // Update the children arrays of the old and new parents
      if (currentCategory.parent_id) {
        // Remove from old parent's children array
        await db.collection('categories').updateOne(
          { _id: currentCategory.parent_id },
          { $pull: { children: id } }
        );
      }

      if (parentId) {
        // Add to new parent's children array
        await db.collection('categories').updateOne(
          { _id: parentId },
          { $addToSet: { children: id } }
        );
      }
    }

    // Revalidate related paths
    revalidatePath('/dashboard/categories');
    revalidatePath(`/dashboard/categories/${id}`);

    // Return success with redirect info
    return createSuccessResponse('Category updated successfully', true, `/dashboard/categories/${id}`);
  } catch (error) {
    console.error('Error updating category:', error);
    return createErrorResponse('Failed to update category', error);
  }
}

/**
 * Delete a category and all its children
 */
export async function deleteCategory(id) {
  try {
    const { db } = await connectToDatabase();

    // Get the category to check if it exists
    const category = await db.collection('categories').findOne({ _id: new ObjectId(id) });
    if (!category) {
      return createErrorResponse('Category not found');
    }

    // Get all descendant categories
    const descendantCategories = await db.collection('categories').find({
      path: { $regex: `/${id}/` }
    }).toArray();

    // Get all category IDs to delete
    const categoryIds = [new ObjectId(id), ...descendantCategories.map(cat => cat._id)];

    // Delete categories
    const result = await db.collection('categories').deleteMany({
      _id: { $in: categoryIds }
    });

    // Revalidate related paths
    revalidatePath('/dashboard/categories');

    return createSuccessResponse(`Category and ${result.deletedCount - 1} subcategories deleted successfully`);
  } catch (error) {
    console.error('Error deleting category:', error);
    return createErrorResponse('Failed to delete category', error);
  }
}

/**
 * Bulk delete categories and their children
 */
export async function bulkDeleteCategories(ids) {
  try {
    const { db } = await connectToDatabase();

    // Convert string IDs to ObjectId
    const objectIds = ids.map(id => new ObjectId(id));

    // Get all categories to delete
    const categoriesToDelete = await db.collection('categories').find({
      _id: { $in: objectIds }
    }).toArray();

    if (categoriesToDelete.length === 0) {
      return createErrorResponse('No categories found to delete');
    }

    // Get all descendant categories
    const descendantQueries = categoriesToDelete.map(cat => ({
      path: { $regex: `/${cat._id.toString()}/` }
    }));

    const descendantCategories = await db.collection('categories').find({
      $or: descendantQueries
    }).toArray();

    // Combine all category IDs to delete
    const allCategoryIds = [
      ...objectIds,
      ...descendantCategories.map(cat => cat._id)
    ];

    // Delete categories
    const result = await db.collection('categories').deleteMany({
      _id: { $in: allCategoryIds }
    });

    // Revalidate related paths
    revalidatePath('/dashboard/categories');

    return createSuccessResponse(`${result.deletedCount} categories deleted successfully`, false, null, {
      deletedCount: result.deletedCount
    });
  } catch (error) {
    console.error('Error bulk deleting categories:', error);
    return createErrorResponse('Failed to delete categories', error);
  }
}

/**
 * Move a category to a new position within its parent
 */
export async function moveCategory(id, targetId, position) {
  try {
    const { db } = await connectToDatabase();

    // Get the source category
    const sourceCategory = await db.collection('categories').findOne({ _id: new ObjectId(id) });
    if (!sourceCategory) {
      return createErrorResponse('Source category not found');
    }

    // Handle different move operations
    if (position === 'inside') {
      // Moving as a child of target
      const targetCategory = await db.collection('categories').findOne({ _id: new ObjectId(targetId) });
      if (!targetCategory) {
        return createErrorResponse('Target category not found');
      }

      // Check for circular reference
      if (targetCategory.path.includes(`/${id}/`)) {
        return createErrorResponse('Cannot move a category to its own descendant');
      }

      // Update source category's parent, level, path, and path_slugs
      const newLevel = targetCategory.level + 1;
      const newPath = `${targetCategory.path}${targetCategory._id.toString()}/`;
      const newPathSlugs = [...(targetCategory.path_slugs || []), targetCategory.slug];

      // Get next position
      const lastCategory = await db
        .collection('categories')
        .find({ parent_id: targetCategory._id })
        .sort({ position: -1 })
        .limit(1)
        .toArray();

      // Start positions from 0 to be consistent with other position calculations
      const newPosition = lastCategory.length > 0 ? lastCategory[0].position + 1 : 0;

      // Update the category
      await db.collection('categories').updateOne(
        { _id: sourceCategory._id },
        {
          $set: {
            parent_id: targetCategory._id,
            level: newLevel,
            path: newPath,
            path_slugs: newPathSlugs,
            position: newPosition,
            updated_at: new Date()
          }
        }
      );

      // Update all children's paths and levels
      await updateChildrenPaths(db, id, newPath, newLevel);

      // Update the children arrays of the old and new parents
      if (sourceCategory.parent_id) {
        // Remove from old parent's children array
        await db.collection('categories').updateOne(
          { _id: sourceCategory.parent_id },
          { $pull: { children: id } }
        );
      }

      // Add to new parent's children array
      await db.collection('categories').updateOne(
        { _id: targetCategory._id },
        { $addToSet: { children: id } }
      );
    } else {
      // Moving before or after a sibling
      const targetCategory = await db.collection('categories').findOne({ _id: new ObjectId(targetId) });
      if (!targetCategory) {
        return createErrorResponse('Target category not found');
      }

      // Ensure source and target have the same parent
      if ((sourceCategory.parent_id === null && targetCategory.parent_id !== null) ||
          (sourceCategory.parent_id !== null && targetCategory.parent_id === null) ||
          (sourceCategory.parent_id !== null && targetCategory.parent_id !== null &&
           !new ObjectId(sourceCategory.parent_id).equals(new ObjectId(targetCategory.parent_id)))) {
        return createErrorResponse('Cannot reorder categories with different parents');
      }

      // Get all siblings
      const siblings = await db
        .collection('categories')
        .find({ parent_id: targetCategory.parent_id })
        .sort({ position: 1 })
        .toArray();

      // Get the original position of the source category
      const sourcePosition = sourceCategory.position;
      const targetPosition = targetCategory.position;

      // Log positions for debugging
      console.log(`Moving category: Source position: ${sourcePosition}, Target position: ${targetPosition}, Direction: ${position}`);

      // First, sort siblings by their current position to ensure consistent order
      siblings.sort((a, b) => a.position - b.position);

      // Remove source from siblings
      const filteredSiblings = siblings.filter(cat => !cat._id.equals(sourceCategory._id));

      // Find target index in the sorted array
      const targetIndex = filteredSiblings.findIndex(cat => cat._id.equals(targetCategory._id));

      if (targetIndex === -1) {
        return createErrorResponse('Target position could not be determined');
      }

      // Determine if we're moving up or down
      const isMovingUp = sourcePosition > targetPosition;

      // Adjust the insert index based on direction and position
      let insertIndex;

      if (position === 'before') {
        // For 'before', always insert at the target index
        insertIndex = targetIndex;
      } else if (position === 'after') {
        if (isMovingUp) {
          // When moving UP and position is 'after', we need to insert at the target index
          // This is the key fix for the upward movement issue
          insertIndex = targetIndex;
        } else {
          // When moving DOWN and position is 'after', we insert after the target
          insertIndex = targetIndex + 1;
        }
      }

      console.log(`Server: Moving ${isMovingUp ? 'UP' : 'DOWN'}, Source position: ${sourcePosition}, Target position: ${targetPosition}, Insert index: ${insertIndex}`);

      // Make sure insertIndex is valid
      const validInsertIndex = Math.max(0, Math.min(insertIndex, filteredSiblings.length));
      filteredSiblings.splice(validInsertIndex, 0, sourceCategory);

      console.log(`Server: Inserting at index ${validInsertIndex} (${position} ${targetCategory.name})`);
      console.log('New order:', filteredSiblings.map(cat => `${cat.name} (${cat._id})`).join(', '));

      // Update positions for all siblings - ensure they are sequential starting from 0
      // Log the final positions for debugging
      console.log('Final positions:');
      filteredSiblings.forEach((cat, index) => {
        console.log(`${cat.name} (${cat._id}): position ${index} (was ${cat.position})`);
      });

      const bulkOps = filteredSiblings.map((cat, index) => ({
        updateOne: {
          filter: { _id: cat._id },
          update: { $set: { position: index, updated_at: new Date() } }
        }
      }));

      await db.collection('categories').bulkWrite(bulkOps);
    }

    // Revalidate related paths
    revalidatePath('/dashboard/categories');

    return createSuccessResponse('Category moved successfully', false, null, {
      sourceId: id,
      targetId: targetId,
      position: position
    });
  } catch (error) {
    console.error('Error moving category:', error);
    return createErrorResponse('Failed to move category', error);
  }
}

/**
 * Helper function to update children's paths and levels when a parent is moved
 */
async function updateChildrenPaths(db, parentId, parentPath, parentLevel) {
  try {
    // Get parent category to get its path_slugs and slug
    const parent = await db.collection('categories').findOne({ _id: new ObjectId(parentId) });
    if (!parent) {
      console.error('Parent category not found in updateChildrenPaths');
      return;
    }

    // Get all direct children
    const children = await db.collection('categories').find({
      parent_id: new ObjectId(parentId)
    }).toArray();

    for (const child of children) {
      const childId = child._id.toString();
      const newLevel = parentLevel + 1;
      const newPath = `${parentPath}${parentId}/`;
      const newPathSlugs = [...(parent.path_slugs || []), parent.slug];

      // Update this child
      await db.collection('categories').updateOne(
        { _id: child._id },
        {
          $set: {
            level: newLevel,
            path: newPath,
            path_slugs: newPathSlugs,
            updated_at: new Date()
          }
        }
      );

      // Recursively update this child's children
      await updateChildrenPaths(db, childId, newPath, newLevel);
    }
  } catch (error) {
    console.error('Error updating children paths:', error);
    throw error;
  }
}