'use server';

import { connectToDatabase } from '@/lib/mongodb';
import { createDeviceSchema, updateDeviceSchema, deviceFilterSchema } from '@/schemas';
import { revalidatePath } from 'next/cache';
import { generateSlug } from '@/lib/utils';
import {
  processFormData,
  convertFormFieldTypes,
  createSuccessResponse,
  createErrorResponse,
  toObjectId,
  toObjectIds
} from '@/lib/action-utils';

/**
 * Get all devices with optional filtering
 */
export async function getDevices(filterData = {}) {
  try {
    const { db } = await connectToDatabase();

    // Build query
    const query = {};

    // Handle special case for series_id: null
    if (filterData.series_id === null) {
      query.series_id = null;
    }

    try {
      // Try to parse and validate filter data
      const filter = deviceFilterSchema.parse(filterData);

      if (filter.name) {
        query.name = { $regex: filter.name, $options: 'i' };
      }
      if (filter.brand_id) {
        query.brand_id = filter.brand_id;
      }
      if (filter.series_id && filter.series_id !== 'none') {
        query.series_id = filter.series_id;
      }
      if (filter.is_active !== undefined) {
        query.is_active = filter.is_active;
      }

      // Calculate pagination
      const skip = (filter.page - 1) * filter.limit;

      // Execute query
      const devices = await db
        .collection('devices')
        .find(query)
        .sort({ [filter.sort]: filter.order === 'asc' ? 1 : -1 })
        .skip(skip)
        .limit(filter.limit)
        .toArray();

      // Get total count for pagination
      const total = await db.collection('devices').countDocuments(query);

      // Convert MongoDB objects to plain objects
      const serializedDevices = devices.map(device => ({
        _id: device._id.toString(),
        name: device.name,
        slug: device.slug,
        brand_id: device.brand_id,
        series_id: device.series_id,
        is_active: device.is_active,
        img: device.img,
        position: device.position,
        created_at: device.created_at,
        updated_at: device.updated_at
      }));

      // Create pagination object
      const pagination = {
        total,
        page: filter.page,
        limit: filter.limit,
        pages: Math.ceil(total / filter.limit),
      };

      return {
        devices: serializedDevices,
        pagination,
      };
    } catch (validationError) {
      // If validation fails, assume we're just querying for devices with specific criteria
      console.log('Validation failed, using direct query:', filterData);

      // Apply filter data directly to query
      if (filterData.brand_id) {
        query.brand_id = filterData.brand_id;
      }

      if (filterData.series_id === 'none') {
        query.series_id = null;
      } else if (filterData.series_id) {
        query.series_id = filterData.series_id;
      }

      if (filterData.is_active !== undefined) {
        query.is_active = filterData.is_active;
      }

      // Execute query without pagination
      const devices = await db
        .collection('devices')
        .find(query)
        .sort({ position: 1 })
        .toArray();

      // Convert MongoDB objects to plain objects
      const serializedDevices = devices.map(device => ({
        _id: device._id.toString(),
        name: device.name,
        slug: device.slug,
        brand_id: device.brand_id,
        series_id: device.series_id,
        is_active: device.is_active,
        img: device.img,
        position: device.position,
        created_at: device.created_at,
        updated_at: device.updated_at
      }));

      return {
        devices: serializedDevices,
        pagination: {
          total: serializedDevices.length,
          page: 1,
          limit: serializedDevices.length,
          pages: 1,
        },
      };
    }
  } catch (error) {
    console.error('Error getting devices:', error);
    throw new Error('Failed to get devices');
  }
}

/**
 * Get a single device by ID
 */
export async function getDeviceById(id) {
  try {
    const { db } = await connectToDatabase();

    // Safely convert ID to ObjectId
    let objectId;
    try {
      objectId = await toObjectId(id);
    } catch (error) {
      console.error('Invalid device ID format:', id);
      throw new Error('Invalid device ID format');
    }

    // Find the device by ID
    const device = await db.collection('devices').findOne({ _id: objectId });

    if (!device) {
      console.error(`Device not found with ID: ${id}`);
      throw new Error('Device not found');
    }

    // Convert MongoDB object to plain object
    return {
      _id: device._id.toString(),
      name: device.name,
      slug: device.slug,
      brand_id: device.brand_id,
      series_id: device.series_id,
      is_active: device.is_active,
      img: device.img,
      position: device.position,
      created_at: device.created_at,
      updated_at: device.updated_at
    };
  } catch (error) {
    console.error('Error getting device:', error);
    throw error; // Rethrow the original error for better error handling
  }
}

/**
 * Get devices by series ID
 */
export async function getDevicesBySeriesId(seriesId, includeInactive = false) {
  try {
    const { db } = await connectToDatabase();

    // Build query
    const query = { series_id: seriesId };
    if (!includeInactive) {
      query.is_active = true;
    }

    // Execute query
    const devices = await db
      .collection('devices')
      .find(query)
      .sort({ position: 1 })
      .toArray();

    // Convert MongoDB objects to plain objects
    return devices.map(device => ({
      _id: device._id.toString(),
      name: device.name,
      slug: device.slug,
      brand_id: device.brand_id,
      series_id: device.series_id,
      is_active: device.is_active,
      img: device.img,
      position: device.position,
      created_at: device.created_at,
      updated_at: device.updated_at
    }));
  } catch (error) {
    console.error('Error getting devices by series:', error);
    throw new Error('Failed to get devices');
  }
}

/**
 * Get devices by brand ID
 */
export async function getDevicesByBrandId(brandId, includeInactive = false) {
  try {
    const { db } = await connectToDatabase();

    // Build query
    const query = { brand_id: brandId };
    if (!includeInactive) {
      query.is_active = true;
    }

    // Execute query
    const devices = await db
      .collection('devices')
      .find(query)
      .sort({ position: 1 })
      .toArray();

    // Convert MongoDB objects to plain objects
    return devices.map(device => ({
      _id: device._id.toString(),
      name: device.name,
      slug: device.slug,
      brand_id: device.brand_id,
      series_id: device.series_id,
      is_active: device.is_active,
      img: device.img,
      position: device.position,
      created_at: device.created_at,
      updated_at: device.updated_at
    }));
  } catch (error) {
    console.error('Error getting devices by brand:', error);
    throw new Error('Failed to get devices');
  }
}

/**
 * Check if a device slug already exists
 * @param {string} slug - The slug to check
 * @param {string} excludeId - Optional ID to exclude from the check (for updates)
 * @returns {Promise<boolean>} - True if the slug exists, false otherwise
 */
async function checkSlugExists(slug, excludeId = null) {
  try {
    const { db } = await connectToDatabase();

    // Build query to check if slug exists
    const query = { slug };

    // If excludeId is provided, exclude that device from the check
    if (excludeId) {
      try {
        const objectId = await toObjectId(excludeId);
        query._id = { $ne: objectId };
      } catch (error) {
        console.error('Invalid exclude ID format:', error);
        // Continue with the check without excluding
      }
    }

    // Check if a device with this slug already exists
    const existingDevice = await db.collection('devices').findOne(query);

    return !!existingDevice;
  } catch (error) {
    console.error('Error checking if slug exists:', error);
    throw new Error('Failed to check if slug exists');
  }
}

/**
 * Create a new device
 */
export async function createDevice(formData) {
  try {
    const { db } = await connectToDatabase();

    // Process form data
    const data = await processFormData(formData);

    // Convert field types
    const convertedData = await convertFormFieldTypes(data);

    // Parse and validate form data
    const deviceData = createDeviceSchema.parse(convertedData);

    // Generate slug if not provided
    if (!deviceData.slug) {
      deviceData.slug = generateSlug(deviceData.name);
    }

    // Check if slug already exists
    const slugExists = await checkSlugExists(deviceData.slug);
    if (slugExists) {
      return await createErrorResponse(`A device with the slug "${deviceData.slug}" already exists. Please choose a different slug.`);
    }

    // Build query for finding the highest position based on brand_id and series_id
    const positionQuery = {};

    // Add brand_id to query if provided
    if (deviceData.brand_id) {
      positionQuery.brand_id = deviceData.brand_id;
    }

    // Add series_id to query if provided and not 'none'
    if (deviceData.series_id && deviceData.series_id !== 'none') {
      positionQuery.series_id = deviceData.series_id;
    } else if (deviceData.series_id === 'none') {
      // If series_id is 'none', find devices with null series_id
      positionQuery.series_id = null;
    }

    // Get the highest position value for devices with the same brand_id and series_id
    const highestPositionDevice = await db
      .collection('devices')
      .find(positionQuery)
      .sort({ position: -1 })
      .limit(1)
      .toArray();

    // Set position to be one higher than the current highest
    const nextPosition = highestPositionDevice.length > 0
      ? highestPositionDevice[0].position + 1
      : 1;

    console.log(`Setting position to ${nextPosition} for device with brand_id=${deviceData.brand_id} and series_id=${deviceData.series_id || 'null'}`);

    // Verify brand exists if brand_id is provided
    if (deviceData.brand_id) {
      try {
        const brandId = await toObjectId(deviceData.brand_id);
        const brand = await db.collection('brands').findOne({ _id: brandId });
        if (!brand) {
          return await createErrorResponse('Brand not found');
        }
      } catch (error) {
        console.error('Error verifying brand:', error);
        return await createErrorResponse('Invalid brand ID format');
      }
    }

    // Verify series exists if series_id is provided and not 'none'
    if (deviceData.series_id && deviceData.series_id !== 'none') {
      try {
        const seriesId = await toObjectId(deviceData.series_id);
        const series = await db.collection('series').findOne({ _id: seriesId });
        if (!series) {
          return await createErrorResponse('Series not found');
        }
      } catch (error) {
        return await createErrorResponse('Invalid series ID format');
      }
    }

    // Convert 'none' to null for series_id
    if (deviceData.series_id === 'none') {
      deviceData.series_id = null;
    }

    // Add timestamps and position
    const now = new Date();
    const deviceToInsert = {
      ...deviceData,
      position: deviceData.position || nextPosition,
      created_at: now,
      updated_at: now,
    };

    // Insert device
    const result = await db.collection('devices').insertOne(deviceToInsert);

    // Revalidate related paths
    revalidatePath('/dashboard/devices');
    if (deviceData.series_id) {
      revalidatePath(`/dashboard/series/${deviceData.series_id}`);
    }

    return await createSuccessResponse(
      'Device created successfully',
      true,
      `/dashboard/devices/${result.insertedId}`,
      { deviceId: result.insertedId.toString() }
    );
  } catch (error) {
    return await createErrorResponse('Failed to create device', error);
  }
}

/**
 * Update an existing device
 */
export async function updateDevice(id, formData) {
  try {
    const { db } = await connectToDatabase();

    // Process form data
    const data = await processFormData(formData);

    // Convert field types
    const convertedData = await convertFormFieldTypes(data);

    // Parse and validate form data
    const deviceData = updateDeviceSchema.parse(convertedData);

    // Remove _id from update data if present
    const { _id, ...updateData } = deviceData;

    // Get the device to check if it exists and to get the current series_id for revalidation
    const objectId = await toObjectId(id);
    const existingDevice = await db.collection('devices').findOne({ _id: objectId });

    if (!existingDevice) {
      return await createErrorResponse('Device not found');
    }

    // Check if slug is being updated and if it already exists
    if (updateData.slug && updateData.slug !== existingDevice.slug) {
      const slugExists = await checkSlugExists(updateData.slug, id);
      if (slugExists) {
        return await createErrorResponse(`A device with the slug "${updateData.slug}" already exists. Please choose a different slug.`);
      }
    }

    // Verify brand exists if brand_id is provided
    if (updateData.brand_id) {
      try {
        const brandId = await toObjectId(updateData.brand_id);
        const brand = await db.collection('brands').findOne({ _id: brandId });
        if (!brand) {
          return await createErrorResponse('Brand not found');
        }
      } catch (error) {
        console.error('Error verifying brand:', error);
        return await createErrorResponse('Invalid brand ID format');
      }
    }

    // Verify series exists if series_id is provided and not 'none'
    if (updateData.series_id && updateData.series_id !== 'none') {
      try {
        const seriesId = await toObjectId(updateData.series_id);
        const series = await db.collection('series').findOne({ _id: seriesId });
        if (!series) {
          return await createErrorResponse('Series not found');
        }
      } catch (error) {
        return await createErrorResponse('Invalid series ID format');
      }
    }

    // Convert 'none' to null for series_id
    if (updateData.series_id === 'none') {
      updateData.series_id = null;
    }

    // Add updated timestamp
    updateData.updated_at = new Date();

    // Update device
    await db.collection('devices').updateOne(
      { _id: objectId },
      { $set: updateData }
    );

    // Revalidate related paths
    revalidatePath('/dashboard/devices');
    revalidatePath(`/dashboard/devices/${id}`);

    // Revalidate both old and new series paths if series_id was changed
    if (updateData.series_id && existingDevice.series_id !== updateData.series_id) {
      revalidatePath(`/dashboard/series/${existingDevice.series_id}`);
      revalidatePath(`/dashboard/series/${updateData.series_id}`);
    } else if (existingDevice.series_id) {
      revalidatePath(`/dashboard/series/${existingDevice.series_id}`);
    }

    return await createSuccessResponse(
      'Device updated successfully',
      true,
      `/dashboard/devices/${id}`
    );
  } catch (error) {
    return await createErrorResponse('Failed to update device', error);
  }
}

/**
 * Delete a device
 */
export async function deleteDevice(id) {
  try {
    const { db } = await connectToDatabase();

    // Get device to get series_id for revalidation
    const objectId = await toObjectId(id);
    const device = await db.collection('devices').findOne({ _id: objectId });

    if (!device) {
      return await createErrorResponse('Device not found');
    }

    // Delete device
    await db.collection('devices').deleteOne({ _id: objectId });

    // Revalidate related paths
    revalidatePath('/dashboard/devices');
    if (device.series_id) {
      revalidatePath(`/dashboard/series/${device.series_id}`);
    }

    return await createSuccessResponse('Device deleted successfully');
  } catch (error) {
    return await createErrorResponse('Failed to delete device', error);
  }
}

/**
 * Bulk delete devices
 */
export async function bulkDeleteDevices(ids) {
  try {
    const { db } = await connectToDatabase();

    // Convert string IDs to ObjectIds
    const objectIds = await toObjectIds(ids);

    // Get devices to get series_ids for revalidation
    const devices = await db
      .collection('devices')
      .find({ _id: { $in: objectIds } })
      .toArray();

    if (devices.length === 0) {
      return await createErrorResponse('No devices found');
    }

    // Delete devices
    await db.collection('devices').deleteMany({ _id: { $in: objectIds } });

    // Revalidate related paths
    revalidatePath('/dashboard/devices');

    // Get unique series IDs and revalidate each series page
    const seriesIds = [...new Set(devices.map(device => device.series_id).filter(Boolean))];
    seriesIds.forEach(seriesId => {
      revalidatePath(`/dashboard/series/${seriesId}`);
    });

    return await createSuccessResponse(`${devices.length} devices deleted successfully`);
  } catch (error) {
    return await createErrorResponse('Failed to delete devices', error);
  }
}
