'use server';

import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { revalidatePath } from 'next/cache';

/**
 * Upload an image to the server
 * @param {FormData} formData - The form data containing the file
 * @returns {Promise<{url: string}>} - The URL of the uploaded image
 */
export async function uploadImage(formData) {
  try {
    if (!formData) {
      return { status: 'error', message: 'No form data provided' };
    }

    const file = formData.get('file');
    const uploadPath = formData.get('path') || 'uploads';

    if (!file) {
      return { status: 'error', message: 'No file provided' };
    }

    // Get file buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Get file extension
    const fileType = file.type;
    const fileExtension = fileType.split('/')[1];

    // Generate unique filename
    const filename = `${uuidv4()}.${fileExtension}`;

    // Create directory if it doesn't exist
    const publicDir = join(process.cwd(), 'public');
    const uploadDir = join(publicDir, uploadPath);

    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (error) {
      console.error('Error creating directory:', error);
      return { status: 'error', message: 'Failed to create upload directory' };
    }

    // Write file to disk
    const filePath = join(uploadDir, filename);
    await writeFile(filePath, buffer);

    // Generate URL
    const url = `/${uploadPath}/${filename}`;

    // Revalidate paths
    revalidatePath('/dashboard/brands');
    revalidatePath('/dashboard/series');
    revalidatePath('/dashboard/devices');
    revalidatePath('/dashboard/categories');

    return {
      status: 'success',
      message: 'Image uploaded successfully',
      url
    };
  } catch (error) {
    console.error('Error uploading image:', error);
    return { status: 'error', message: 'Failed to upload image' };
  }
}
