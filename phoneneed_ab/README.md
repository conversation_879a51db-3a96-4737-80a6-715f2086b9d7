# PhoneNeed

PhoneNeed is a comprehensive catalog management system for mobile phones, built with Next.js, MongoDB, and Shadcn UI components.

## Features

- **Brand Management**: Create, read, update, and delete phone brands
- **Series Management**: Organize phones into series within brands
- **Device Management**: Manage individual phone models with detailed information
- **Hierarchical Organization**: Devices belong to series, which belong to brands
- **Responsive UI**: Modern, responsive interface built with Shadcn UI components
- **Server Actions**: Utilizes Next.js server actions for data operations
- **Form Validation**: Robust form validation with <PERSON>od and React Hook Form
- **Image Support**: Upload and display images for brands, series, and devices

## Tech Stack

- **Frontend**: Next.js, React, Tailwind CSS, Shadcn UI
- **Backend**: Next.js API routes, Server Actions
- **Database**: MongoDB
- **Validation**: Zod
- **Forms**: React Hook Form
- **State Management**: React Hooks (useState, useEffect, useTransition)
- **Routing**: Next.js App Router

## Project Structure

```
phoneneed_ab/
├── actions/             # Server actions for CRUD operations
├── app/                 # Next.js app directory
│   ├── dashboard/       # Dashboard pages
│   │   ├── brands/      # Brand management
│   │   ├── series/      # Series management
│   │   └── devices/     # Device management
├── components/          # Reusable components
│   ├── data-display/    # Data display components
│   ├── filters/         # Filter components
│   ├── forms/           # Form components
│   ├── layout/          # Layout components
│   └── ui/              # UI components (Shadcn)
├── lib/                 # Utility functions
├── public/              # Static assets
└── schemas/             # Zod validation schemas
```

## Data Models

### Brand

```javascript
{
  _id: ObjectId,
  name: String,
  slug: String,
  is_active: Boolean,
  img: String,
  position: Number,
  created_at: Date,
  updated_at: Date
}
```

### Series

```javascript
{
  _id: ObjectId,
  name: String,
  slug: String,
  brand_id: String,
  is_active: Boolean,
  img: String,
  position: Number,
  created_at: Date,
  updated_at: Date
}
```

### Device

```javascript
{
  _id: ObjectId,
  name: String,
  slug: String,
  brand_id: String,
  series_id: String, // Optional, can be null
  is_active: Boolean,
  img: String,
  position: Number,
  created_at: Date,
  updated_at: Date
}
```

## Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables:
   - Create a `.env.local` file with your MongoDB connection string
4. Run the development server: `npm run dev`
5. Open [http://localhost:3000](http://localhost:3000) in your browser

## Environment Variables

Create a `.env.local` file with the following variables:

```
MONGODB_URI=your_mongodb_connection_string
```

## License

This project is licensed under the MIT License.
