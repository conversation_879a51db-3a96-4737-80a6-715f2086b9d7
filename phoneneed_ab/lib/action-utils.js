'use server';

import { ObjectId } from 'mongodb';

/**
 * Serialize a MongoDB document to a plain JavaScript object
 * Converts ObjectId to string and preserves other properties
 * @param {Object} doc - MongoDB document
 * @returns {Object} - Plain JavaScript object
 */
export async function serializeDocument(doc) {
  if (!doc) return null;

  return {
    ...doc,
    _id: doc._id.toString(),
  };
}

/**
 * Serialize an array of MongoDB documents to plain JavaScript objects
 * @param {Array} docs - Array of MongoDB documents
 * @returns {Array} - Array of plain JavaScript objects
 */
export async function serializeDocuments(docs) {
  if (!docs || !Array.isArray(docs)) return [];

  // Use a simpler approach that doesn't rely on async/await in a loop
  return docs.map(doc => {
    if (!doc) return null;
    return {
      ...doc,
      _id: doc._id.toString(),
    };
  });
}

/**
 * Process form data from either FormData or plain object
 * @param {FormData|Object} formData - Form data to process
 * @returns {Object} - Processed form data
 */
export async function processFormData(formData) {
  // Convert FormData to object if needed
  let data = formData instanceof FormData
    ? Object.fromEntries(formData)
    : formData;

  return data;
}

/**
 * Convert form field types to appropriate JavaScript types
 * @param {Object} data - Form data object
 * @returns {Object} - Form data with converted types
 */
export async function convertFormFieldTypes(data) {
  const result = { ...data };

  // Convert position to number
  if (result.position) {
    result.position = parseInt(result.position, 10);
  }

  // Convert is_active to boolean
  if (result.is_active === 'true' || result.is_active === true) {
    result.is_active = true;
  } else {
    result.is_active = false;
  }

  return result;
}

/**
 * Create pagination result object
 * @param {Array} items - Array of items
 * @param {number} total - Total count of items
 * @param {Object} filter - Filter object with page and limit
 * @param {string} itemName - Name of the items (e.g., 'brands', 'series')
 * @returns {Object} - Object with items and pagination info
 */
export async function createPaginationResult(items, total, filter, itemName) {
  return {
    [itemName]: items,
    pagination: {
      total,
      page: filter.page,
      limit: filter.limit,
      pages: Math.ceil(total / filter.limit),
    },
  };
}

/**
 * Create a success response object
 * @param {string} message - Success message
 * @param {boolean} redirect - Whether to redirect
 * @param {string} redirectUrl - URL to redirect to
 * @param {Object} additionalData - Additional data to include in response
 * @returns {Object} - Success response object
 */
export async function createSuccessResponse(message, redirect = false, redirectUrl = null, additionalData = {}) {
  return {
    success: true,
    message,
    redirect,
    redirectUrl,
    ...additionalData,
  };
}

/**
 * Create an error response object
 * @param {string} message - Error message
 * @param {Error} error - Original error object
 * @returns {Object} - Error response object
 */
export async function createErrorResponse(message, error = null) {
  if (error) {
    console.error(message, error);
    console.error('Error stack:', error.stack);

    // If it's a ZodError, log the specific validation issues
    if (error.name === 'ZodError' && error.issues) {
      console.error('Validation errors:', JSON.stringify(error.issues, null, 2));
    }
  }

  return {
    success: false,
    message: error?.message || message,
    redirect: false,
  };
}

/**
 * Convert string ID to MongoDB ObjectId
 * @param {string} id - String ID
 * @returns {Promise<ObjectId>} - MongoDB ObjectId
 */
export async function toObjectId(id) {
  try {
    // Wrap in Promise.resolve to ensure it's always a Promise
    return Promise.resolve(new ObjectId(id));
  } catch (error) {
    console.error(`Invalid ObjectId format: ${id}`);
    throw new Error(`Invalid ObjectId format: ${id}`);
  }
}

/**
 * Convert array of string IDs to MongoDB ObjectIds
 * @param {string[]} ids - Array of string IDs
 * @returns {Promise<ObjectId[]>} - Array of MongoDB ObjectIds
 */
export async function toObjectIds(ids) {
  // Use Promise.all to handle the array of promises
  return Promise.all(ids.map(id => toObjectId(id)));
}
