import { clsx } from "clsx";
import { twMerge } from "tailwind-merge"

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

/**
 * Replaces "+" with "Plus" in a string
 * Handles both "Galaxy S25+" and "Galaxy S25 +" formats
 *
 * @param {string} str - The string to process
 * @returns {string} - The string with "+" replaced by "Plus"
 */
export const replacePlusWithPlus = (str) => {
  if (!str) return '';

  // Replace "+ " or " +" with " Plus "
  let result = str.replace(/\+\s|\s\+/g, ' Plus ');

  // Replace remaining "+" with " Plus"
  result = result.replace(/\+/g, ' Plus');

  // Normalize multiple spaces
  return result.replace(/\s+/g, ' ').trim();
};

/**
 * Generates a URL-friendly slug from a string
 * - Replaces "+" with "Plus"
 * - Normalizes accented characters
 * - Removes diacritics
 * - Converts to lowercase
 * - Removes invalid characters
 * - Replaces whitespace with hyphens
 * - Collapses multiple hyphens
 * - Trims leading/trailing hyphens
 *
 * @param {string} str - The string to convert to a slug
 * @returns {string} - The generated slug
 */
export const generateSlug = (str) => {
  if (!str) return '';

  return replacePlusWithPlus(str)
    .normalize('NFKD') // Normalize accented characters
    .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove invalid characters
    .replace(/\s+/g, '-') // Replace whitespace with hyphens
    .replace(/-+/g, '-') // Collapse multiple hyphens
    .replace(/^-+|-+$/g, ''); // Trim leading/trailing hyphens
};

/**
 * Convert a string to a URL-friendly slug
 * @param {string} text - The text to convert to a slug
 * @returns {string} The slugified text
 */
export function slugify(text) {
  return generateSlug(text);
}

/**
 * Format a date string or object to a readable format
 * @param {string|Date} date - The date to format
 * @param {object} options - Intl.DateTimeFormat options
 * @returns {string} The formatted date
 */
export function formatDate(date, options = {}) {
  if (!date) return '—';

  const defaultOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    ...options
  };

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('en-US', defaultOptions).format(dateObj);
  } catch (error) {
    console.error('Error formatting date:', error);
    return String(date);
  }
}
