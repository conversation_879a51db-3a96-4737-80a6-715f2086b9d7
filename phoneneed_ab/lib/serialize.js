/**
 * Serializes MongoDB documents to plain JavaScript objects
 * This is necessary when passing data from server components to client components
 *
 * @param {Object|Array} data - MongoDB document or array of documents
 * @returns {Object|Array} - Serialized data
 */
export function serializeData(data, seen = new WeakMap()) {
  // Handle null or undefined
  if (data == null) {
    return data;
  }

  // Handle primitive values
  if (typeof data !== 'object') {
    return data;
  }

  // Handle dates
  if (data instanceof Date) {
    return data.toISOString();
  }

  // Detect circular references
  if (seen.has(data)) {
    return null; // or some other placeholder for circular references
  }

  // Add this object to the seen map
  seen.set(data, true);

  // Handle arrays
  if (Array.isArray(data)) {
    return data.map(item => serializeData(item, seen));
  }

  // Handle ObjectId directly
  if (data._id && typeof data._id === 'object' && data._id.toString) {
    const id = data._id.toString();

    // Create a new object without the _id to avoid circular reference
    const { _id, ...rest } = data;

    // Return a new object with the string _id
    return {
      _id: id,
      ...serializeData(rest, seen)
    };
  }

  // Handle plain objects
  const serialized = {};

  for (const [key, value] of Object.entries(data)) {
    // Skip functions and symbols
    if (typeof value === 'function' || typeof value === 'symbol') {
      continue;
    }

    // Handle ObjectId fields
    if (key === '_id' && typeof value === 'object' && value && value.toString) {
      serialized[key] = value.toString();
    }
    // Handle parent_id and other ID references
    else if ((key === 'parent_id' || key.endsWith('_id')) && typeof value === 'object' && value && value.toString) {
      serialized[key] = value.toString();
    }
    // Recursively serialize nested objects and arrays
    else {
      serialized[key] = serializeData(value, seen);
    }
  }

  return serialized;
}
