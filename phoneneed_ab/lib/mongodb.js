import { MongoClient } from 'mongodb';

/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */
let cached = global.mongo;

if (!cached) {
  cached = global.mongo = { conn: null, promise: null };
}

/**
 * Connect to MongoDB and return the database client and database instance
 * @returns {Promise<{client: MongoClient, db: Db}>} A promise that resolves to the MongoDB client and database
 */
export async function connectToDatabase() {
  // If we have a connection already, return it
  if (cached.conn) {
    return cached.conn;
  }

  // If we don't have a connection but we have a connecting promise, await it
  if (!cached.promise) {
    // Check if MONGODB_URI is defined
    if (!process.env.MONGODB_URI) {
      throw new Error(
        'Please define the MONGODB_URI environment variable inside .env.local'
      );
    }

    const uri = process.env.MONGODB_URI;
    const options = {
      maxPoolSize: 10, // Maintain up to 10 socket connections
    };

    // Connect to the MongoDB server
    cached.promise = MongoClient.connect(uri, options).then((client) => {
      // Get the database name from the connection string
      const dbName = new URL(uri).pathname.substring(1) || 'shopify_management';

      // Return both the client and the database
      return {
        client,
        db: client.db(dbName),
      };
    });
  }

  try {
    cached.conn = await cached.promise;
  } catch (e) {
    cached.promise = null;
    console.error('MongoDB connection error:', e);
    throw e;
  }

  return cached.conn;
}

/**
 * Disconnect from MongoDB
 * Useful for testing environments
 */
export async function disconnectFromDatabase() {
  if (cached.conn) {
    await cached.conn.client.close();
    cached.conn = null;
    cached.promise = null;
    console.log('MongoDB disconnected');
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  await disconnectFromDatabase();
  process.exit(0);
});
