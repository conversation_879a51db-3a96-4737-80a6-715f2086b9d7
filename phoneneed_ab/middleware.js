import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

// This function can be marked `async` if using `await` inside
export default withAuth(
  // `withAuth` augments your `Request` with the user's token.
  function middleware(req) {
    // You can use the token to make additional checks or modify the response
    console.log("Token:", req.nextauth.token);
    
    // If you want to redirect to a different page based on the token
    // return NextResponse.redirect(new URL("/custom-page", req.url));
    
    // Otherwise, continue with the request
    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token }) => !!token, // Only allow authenticated users
    },
    pages: {
      signIn: "/auth/signin", // Where to redirect for sign in
    },
  }
);

// Specify the paths that should be protected by this middleware
export const config = {
  matcher: ["/dashboard/:path*"],
};
