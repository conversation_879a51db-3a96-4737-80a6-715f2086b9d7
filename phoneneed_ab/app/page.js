"use client";

import Image from "next/image";
import { Button } from "@/components/ui/button";
import { useSession, signIn, signOut } from "next-auth/react";
import Link from "next/link";

export default function Home() {
  const { data: session, status } = useSession();
  return (
    <div className="grid grid-rows-[auto_1fr_auto] items-center min-h-screen p-8 pb-20 gap-8 sm:p-20 font-[family-name:var(--font-geist-sans)]">
      <header className="w-full flex justify-between items-center">
        <h1 className="text-3xl font-bold text-primary">PhoneNeed</h1>
        <div>
          {status === "authenticated" ? (
            <div className="flex items-center gap-4">
              <span>Welcome, {session.user.name || session.user.email}</span>
              <div className="flex gap-2">
                <Link href="/dashboard">
                  <Button variant="outline">Dashboard</Button>
                </Link>
                <Button variant="ghost" onClick={() => signOut()}>
                  Sign Out
                </Button>
              </div>
            </div>
          ) : (
            <Button onClick={() => signIn()}>Sign In</Button>
          )}
        </div>
      </header>

      <main className="flex flex-col gap-8 items-center justify-center">
        <div className="text-center max-w-2xl">
          <h2 className="text-4xl font-bold mb-4">Welcome to PhoneNeed</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Your one-stop shop for all your phone needs
          </p>

          <div className="flex gap-4 justify-center">
            <Button size="lg">Shop Now</Button>
            <Link href="/dashboard">
              <Button variant="outline" size="lg">
                {status === "authenticated" ? "Go to Dashboard" : "Learn More"}
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 w-full max-w-4xl">
          <div className="bg-card p-6 rounded-lg shadow-sm">
            <h3 className="text-xl font-semibold mb-2">Latest Phones</h3>
            <p className="text-muted-foreground mb-4">
              Discover the newest smartphones with cutting-edge features
            </p>
            <Button variant="link">Browse Collection →</Button>
          </div>

          <div className="bg-card p-6 rounded-lg shadow-sm">
            <h3 className="text-xl font-semibold mb-2">Accessories</h3>
            <p className="text-muted-foreground mb-4">
              Find the perfect accessories to complement your device
            </p>
            <Button variant="link">Explore Accessories →</Button>
          </div>

          <div className="bg-card p-6 rounded-lg shadow-sm">
            <h3 className="text-xl font-semibold mb-2">Repair Services</h3>
            <p className="text-muted-foreground mb-4">
              Professional repair services for all major phone brands
            </p>
            <Button variant="link">Book Service →</Button>
          </div>
        </div>
      </main>

      <footer className="w-full text-center text-sm text-muted-foreground mt-12">
        <p>© 2024 PhoneNeed. All rights reserved.</p>
      </footer>
    </div>
  );
}
