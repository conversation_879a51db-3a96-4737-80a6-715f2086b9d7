'use server';

import { getAvailableBrandsFromGSMArena, importSelectedBrands } from '@/actions/brand.actions';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    console.log('API: Fetching available brands from GSMArena...');

    const result = await getAvailableBrandsFromGSMArena();

    console.log('API: Result from getAvailableBrandsFromGSMArena:', result);

    if (result && result.success) {
      console.log('API: Returning success response with data:', result.data);
      return NextResponse.json({
        success: true,
        message: result.message,
        data: result.data
      });
    } else {
      console.log('API: Returning error response:', result?.message || 'Unknown error');
      return NextResponse.json({
        success: false,
        message: result?.message || 'Unknown error'
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in brands fetch API:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch brands from GSMArena'
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { selectedBrandIds } = body;

    console.log('Starting brands import from API route...');

    const result = await importSelectedBrands(selectedBrandIds);

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        data: result.data
      });
    } else {
      return NextResponse.json({
        success: false,
        message: result.message
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in brands import API:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to import brands from GSMArena'
    }, { status: 500 });
  }
}
