'use server';

import { getAvailableBrandsFromGSMArena, importSelectedBrands, clearGSMArenaBrandsCache } from '@/actions/brand.actions';
import { NextResponse } from 'next/server';

export async function GET(request) {
  try {
    // Check if cache refresh is requested
    const { searchParams } = new URL(request.url);
    const refresh = searchParams.get('refresh');

    if (refresh === 'true') {
      clearGSMArenaBrandsCache();
    }

    const result = await getAvailableBrandsFromGSMArena();

    if (result?.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        data: result.data
      });
    } else {
      return NextResponse.json({
        success: false,
        message: result?.message || 'Unknown error'
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in brands fetch API:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch brands from GSMArena'
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { selectedBrandIds } = body;

    const result = await importSelectedBrands(selectedBrandIds);

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        data: result.data
      });
    } else {
      return NextResponse.json({
        success: false,
        message: result.message
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in brands import API:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to import brands from GSMArena'
    }, { status: 500 });
  }
}
