'use server';

import { syncBrandsFromGSMArena } from '@/actions/brand.actions';
import { NextResponse } from 'next/server';

export async function POST() {
  try {
    console.log('Starting brands sync from API route...');
    
    const result = await syncBrandsFromGSMArena();
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        data: result.data
      });
    } else {
      return NextResponse.json({
        success: false,
        message: result.message
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in brands sync API:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to sync brands from GSMArena'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST method to sync brands from GSMArena',
    endpoint: '/api/sync/brands',
    method: 'POST'
  });
}
