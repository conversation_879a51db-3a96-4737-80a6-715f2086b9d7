import { getBrands } from '@/actions/brand.actions';
import { PageHeader } from '@/components/layout';
import { EmptyState } from '@/components/data-display';
import { Tag } from 'lucide-react';
import { brandColumns } from './columns';
import { Pa<PERSON>ationHandler } from './pagination-handler';
import { SyncBrandsButton } from './_components/sync-brands-button';

export const metadata = {
  title: 'Brands - PhoneNeed',
  description: 'Manage phone brands in your catalog',
};

export default async function BrandsPage(props) {
  // Parse search params for pagination
  const searchParams = await props.searchParams;
  const pageParam = Number(searchParams?.page) || 1;
  const sort = 'position';
  const order = 'asc';

  // Ensure page is a valid number
  const page = isNaN(pageParam) ? 1 : pageParam;

  // Fetch brands with pagination
  const { brands, pagination } = await getBrands({
    page,
    sort,
    order,
    limit: 10,
  });

  // Handle pagination changes - this is passed to the DataTable component
  // The actual implementation is in the client component that updates the URL

  return (
    <div className="space-y-6">
      <PageHeader
        title="Brands"
        description="Manage phone brands in your catalog"
        actionHref="/dashboard/brands/new"
        actionLabel="Add Brand"
      >
        <SyncBrandsButton />
      </PageHeader>

      {brands.length === 0 ? (
        <EmptyState
          title="No brands found"
          description="Get started by creating your first brand."
          icon={Tag}
          actionHref="/dashboard/brands/new"
          actionLabel="Add Brand"
        />
      ) : (
        <div className="space-y-4">
          <PaginationHandler
            columns={brandColumns}
            data={brands}
            pagination={pagination}
          />
        </div>
      )}
    </div>
  );
}
