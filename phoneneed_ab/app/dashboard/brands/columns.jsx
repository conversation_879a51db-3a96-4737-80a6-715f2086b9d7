"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { formatDate } from "@/lib/utils";
import { Eye, Pencil, Trash2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { deleteBrand } from "@/actions/brand.actions";
import { toast } from "sonner";

export const brandColumns = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "img",
    header: "Image",
    cell: ({ row }) => {
      const img = row.getValue("img");
      return img ? (
        <div className="relative h-10 w-10 rounded-md overflow-hidden">
          <Image
            src={img}
            alt={row.getValue("name")}
            fill
            className="object-cover"
          />
        </div>
      ) : (
        <div className="h-10 w-10 rounded-md bg-muted flex items-center justify-center text-muted-foreground">
          No img
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => <div className="font-medium">{row.getValue("name")}</div>,
  },
  {
    accessorKey: "slug",
    header: "Slug",
    cell: ({ row }) => <div className="text-muted-foreground">{row.getValue("slug")}</div>,
  },
  {
    accessorKey: "is_active",
    header: "Status",
    cell: ({ row }) => {
      const isActive = row.getValue("is_active");
      return (
        <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          isActive
            ? "bg-primary text-primary-foreground"
            : "bg-destructive text-destructive-foreground"
        }`}>
          {isActive ? "Active" : "Inactive"}
        </div>
      );
    },
  },
  {
    accessorKey: "position",
    header: "Position",
    cell: ({ row }) => <div className="text-center">{String(row.getValue("position"))}</div>,
  },
  {
    accessorKey: "created_at",
    header: "Created",
    cell: ({ row }) => {
      const date = row.getValue("created_at");
      return <div>{formatDate(date)}</div>;
    },
    enableSorting: true,
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const brand = row.original;
      const router = useRouter();
      const [isDeleting, setIsDeleting] = useState(false);

      const handleDelete = async () => {
        if (confirm(`Are you sure you want to delete ${brand.name}?`)) {
          setIsDeleting(true);
          try {
            const result = await deleteBrand(brand._id);
            if (result.success) {
              toast.success(result.message);
              router.refresh();
            } else {
              toast.error(result.message);
            }
          } catch (error) {
            toast.error("An error occurred while deleting the brand");
            console.error(error);
          } finally {
            setIsDeleting(false);
          }
        }
      };

      return (
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" asChild>
            <Link href={`/dashboard/brands/${brand._id}`}>
              <Eye className="h-4 w-4" />
              <span className="sr-only">View</span>
            </Link>
          </Button>
          <Button variant="ghost" size="icon" asChild>
            <Link href={`/dashboard/brands/${brand._id}/edit`}>
              <Pencil className="h-4 w-4" />
              <span className="sr-only">Edit</span>
            </Link>
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">Delete</span>
          </Button>
        </div>
      );
    },
  },
];
