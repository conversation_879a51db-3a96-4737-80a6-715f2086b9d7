"use client";

import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { DataTable } from "@/components/data-display";
import { useState } from "react";
import { bulkDeleteBrands } from "@/actions/brand.actions";
import { toast } from "sonner";

export function PaginationHandler({ columns, data, pagination }) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isDeleting, setIsDeleting] = useState(false);
  const [shouldResetSelection, setShouldResetSelection] = useState(false);

  // Handle pagination changes
  const handlePaginationChange = ({ page }) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", String(page));
    router.push(`${pathname}?${params.toString()}`);
  };

  // Handle bulk delete
  const handleBulkDelete = async (selectedRows) => {
    if (confirm(`Are you sure you want to delete ${selectedRows.length} selected brands?`)) {
      setIsDeleting(true);
      try {
        // Extract IDs from selected rows
        const ids = selectedRows.map(row => row._id);

        // Call the bulk delete action
        const result = await bulkDeleteBrands(ids);

        if (result.success) {
          toast.success(result.message);
          // Reset selection state
          setShouldResetSelection(true);
          // Refresh the page data
          router.refresh();
        } else {
          toast.error(result.message);
        }
      } catch (error) {
        toast.error("An error occurred while deleting brands");
        console.error(error);
      } finally {
        setIsDeleting(false);
        // Reset the shouldResetSelection flag after a short delay
        setTimeout(() => {
          setShouldResetSelection(false);
        }, 100);
      }
    }
  };

  return (
    <DataTable
      columns={columns}
      data={data}
      pagination={pagination}
      onPaginationChange={handlePaginationChange}
      onBulkDelete={handleBulkDelete}
      isDeleting={isDeleting}
      resetSelection={shouldResetSelection}
    />
  );
}
