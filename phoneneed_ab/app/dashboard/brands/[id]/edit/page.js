import { getBrandById } from '@/actions/brand.actions';
import { notFound } from 'next/navigation';
import { BrandForm } from '../../_components/brand-form';
import { PageHeader } from '@/components/layout';

export const metadata = {
  title: 'Edit Brand - PhoneNeed',
  description: 'Edit brand details',
};

export default async function EditBrandPage(props) {
  try {
    // Await the params object before accessing its properties
    const params = await props.params;
    const id = params.id;

    // Fetch brand details
    const brand = await getBrandById(id);

    if (!brand) {
      notFound();
    }

    return (
      <div className="space-y-6">
        <PageHeader
          title="Edit Brand"
          description="Update brand information"
          backHref={`/dashboard/brands/${id}`}
          backLabel="Back to Brand Details"
        />

        <BrandForm brand={brand} />
      </div>
    );
  } catch (error) {
    console.error('Error loading brand for editing:', error);
    notFound();
  }
}
