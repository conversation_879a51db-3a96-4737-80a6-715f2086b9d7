import { getBrandById } from '@/actions/brand.actions';
import { getSeriesByBrandId } from '@/actions/series.actions';
import { getDevicesByBrandId } from '@/actions/device.actions';
import { PageHeader } from '@/components/layout';
import { EntityDetails } from '@/components/data-display';
import { formatDate } from '@/lib/utils';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Layers, Smartphone } from 'lucide-react';
import Image from 'next/image';

export const metadata = {
  title: 'Brand Details - PhoneNeed',
  description: 'View brand details and related series',
};

export default async function BrandDetailPage(props) {
  try {
    // Await the params object before accessing its properties
    const params = await props.params;
    const id = params.id;

    // Fetch brand details
    let brand;
    try {
      brand = await getBrandById(id);
    } catch (error) {
      console.error(`Error fetching brand with ID ${id}:`, error);
      notFound();
    }

    // Fetch related series
    let series = [];
    try {
      series = await getSeriesByBrandId(id, true);
    } catch (error) {
      console.error(`Error fetching series for brand ${id}:`, error);
      // Continue with empty series array
    }

    // Fetch devices for this brand
    let devices = [];
    try {
      devices = await getDevicesByBrandId(id, true);
    } catch (error) {
      console.error(`Error fetching devices for brand ${id}:`, error);
      // Continue with empty devices array
    }

    // Create a map of series IDs to series names
    const seriesMap = series.reduce((map, item) => {
      map[item._id] = item.name;
      return map;
    }, {});

    // Add series names to devices
    const devicesWithSeriesNames = devices.map(device => ({
      ...device,
      series_name: device.series_id ? seriesMap[device.series_id] || "Unknown Series" : null
    }));

    // Pre-format dates on the server side
    const formattedBrand = {
      ...brand,
      created_at_formatted: formatDate(brand.created_at),
      updated_at_formatted: formatDate(brand.updated_at)
    };

    // Define fields to display
    const fields = [
      { key: 'name', label: 'Brand Name' },
      { key: 'slug', label: 'Slug' },
      { key: 'is_active', label: 'Status' },
      { key: 'position', label: 'Position' },
      { key: 'created_at_formatted', label: 'Created At' },
      { key: 'updated_at_formatted', label: 'Updated At' },
    ];

    return (
      <div className="space-y-6">
        <PageHeader
          title="Brand Details"
          description="View and manage brand information"
          backHref="/dashboard/brands"
          backLabel="Back to Brands"
        >
          <Button asChild>
            <Link href={`/dashboard/brands/${id}/edit`}>
              Edit Brand
            </Link>
          </Button>
        </PageHeader>

        <EntityDetails
          title={brand.name}
          description="Brand details and information"
          entity={formattedBrand}
          fields={fields}
          editHref={`/dashboard/brands/${id}/edit`}
          imageField="img"
        />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Series</h2>
            <div className="flex space-x-2">
              <Button variant="outline" asChild>
                <Link href={`/dashboard/series?brand_id=${id}`}>
                  View All Series
                </Link>
              </Button>
              <Button asChild>
                <Link href={`/dashboard/series/new?brand_id=${id}`}>
                  Add Series
                </Link>
              </Button>
            </div>
          </div>

          {series.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col items-center justify-center p-6 text-center">
                  <Layers className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No Series Found</h3>
                  <p className="text-sm text-muted-foreground mt-1 mb-4">
                    This brand doesn't have any series yet.
                  </p>
                  <Button asChild>
                    <Link href={`/dashboard/series/new?brand_id=${id}`}>
                      Add Series
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {series.map((item) => (
                <Link key={item._id} href={`/dashboard/series/${item._id}`}>
                  <Card className="hover:bg-muted/50 transition-colors cursor-pointer h-full">
                    <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
                      <CardTitle className="text-sm font-medium">{item.name}</CardTitle>
                      <div className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                        item.is_active
                          ? "bg-primary text-primary-foreground"
                          : "bg-destructive text-destructive-foreground"
                      }`}>
                        {item.is_active ? "Active" : "Inactive"}
                      </div>
                    </CardHeader>
                    <CardContent className="pt-2">
                      {item.img && (
                        <div className="relative h-32 w-full rounded-md overflow-hidden mb-3">
                          <Image
                            src={item.img}
                            alt={item.name}
                            fill
                            className="object-contain"
                          />
                        </div>
                      )}
                      <p className="text-xs text-muted-foreground">
                        Position: {item.position}
                      </p>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          )}
        </div>

        {/* Devices Section */}
        <div className="space-y-4 mt-8">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Devices</h2>
            <div className="flex space-x-2">
              <Button variant="outline" asChild>
                <Link href={`/dashboard/devices?brand_id=${id}`}>
                  View All Devices
                </Link>
              </Button>
              <Button asChild>
                <Link href={`/dashboard/devices/new?brand_id=${id}`}>
                  Add Device
                </Link>
              </Button>
            </div>
          </div>

          {devicesWithSeriesNames.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col items-center justify-center p-6 text-center">
                  <Smartphone className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No Devices Found</h3>
                  <p className="text-sm text-muted-foreground mt-1 mb-4">
                    This brand doesn't have any devices yet.
                  </p>
                  <Button asChild>
                    <Link href={`/dashboard/devices/new?brand_id=${id}`}>
                      Add Device
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {devicesWithSeriesNames.map((device) => (
                <Link key={device._id} href={`/dashboard/devices/${device._id}`}>
                  <Card className="hover:bg-muted/50 transition-colors cursor-pointer h-full">
                    <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
                      <CardTitle className="text-sm font-medium">{device.name}</CardTitle>
                      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        device.is_active
                          ? "bg-primary text-primary-foreground"
                          : "bg-destructive text-destructive-foreground"
                      }`}>
                        {device.is_active ? "Active" : "Inactive"}
                      </div>
                    </CardHeader>
                    <CardContent className="pt-2">
                      {device.img && (
                        <div className="relative h-32 w-full rounded-md overflow-hidden mb-3">
                          <Image
                            src={device.img}
                            alt={device.name}
                            fill
                            className="object-contain"
                          />
                        </div>
                      )}
                      <p className="text-xs text-muted-foreground">
                        Position: {device.position}
                      </p>
                      {device.series_id && (
                        <p className="text-xs text-primary mt-1">
                          Series: {device.series_name || "Unknown Series"}
                        </p>
                      )}
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error loading brand details:', error);
    notFound();
  }
}
