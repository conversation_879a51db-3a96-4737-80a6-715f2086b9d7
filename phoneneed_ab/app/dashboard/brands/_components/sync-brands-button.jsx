"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Download, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";

export function SyncBrandsButton() {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState("");
  const router = useRouter();

  const handleSync = async () => {
    setIsLoading(true);
    setMessage("");

    try {
      const response = await fetch('/api/sync/brands', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        setMessage(`✅ ${result.message}`);
        // Refresh the page to show new brands
        router.refresh();
      } else {
        setMessage(`❌ ${result.message}`);
      }
    } catch (error) {
      console.error('Error syncing brands:', error);
      setMessage('❌ Failed to sync brands');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col gap-2">
      <Button
        onClick={handleSync}
        disabled={isLoading}
        variant="outline"
        size="sm"
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Syncing...
          </>
        ) : (
          <>
            <Download className="mr-2 h-4 w-4" />
            Sync from GSMArena
          </>
        )}
      </Button>
      {message && (
        <p className="text-sm text-muted-foreground">{message}</p>
      )}
    </div>
  );
}
