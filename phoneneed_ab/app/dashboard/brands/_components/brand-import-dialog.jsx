"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Download, Loader2, Search, CheckCircle } from "lucide-react";
import { useRouter } from "next/navigation";

export function BrandImportDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [brands, setBrands] = useState([]);
  const [filteredBrands, setFilteredBrands] = useState([]);
  const [selectedBrands, setSelectedBrands] = useState(new Set());
  const [searchTerm, setSearchTerm] = useState("");
  const [message, setMessage] = useState("");
  const [stats, setStats] = useState({ total: 0, imported: 0, available: 0 });
  const router = useRouter();

  // Fetch available brands when dialog opens
  useEffect(() => {
    if (isOpen && brands.length === 0) {
      fetchAvailableBrands();
    }
  }, [isOpen]);

  // Filter brands based on search term
  useEffect(() => {
    if (searchTerm) {
      const filtered = brands.filter(brand =>
        brand.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredBrands(filtered);
    } else {
      setFilteredBrands(brands);
    }
  }, [brands, searchTerm]);

  const fetchAvailableBrands = async () => {
    setIsLoading(true);
    setMessage("");

    try {
      const response = await fetch('/api/sync/brands');
      const result = await response.json();

      if (result.success && result.data?.brands) {
        setBrands(result.data.brands);
        setFilteredBrands(result.data.brands);
        setStats(result.data);
        setMessage(`✅ Found ${result.data.total} brands (${result.data.imported} already imported)`);
      } else {
        setMessage(`❌ ${result.message || 'Invalid response format from server'}`);
      }
    } catch (error) {
      console.error('Error fetching brands:', error);
      setMessage('❌ Failed to fetch brands');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBrandToggle = (brandId, isImported) => {
    if (isImported) return; // Don't allow selecting already imported brands

    const newSelected = new Set(selectedBrands);
    if (newSelected.has(brandId)) {
      newSelected.delete(brandId);
    } else {
      newSelected.add(brandId);
    }
    setSelectedBrands(newSelected);
  };

  const handleSelectAll = () => {
    const availableBrands = filteredBrands.filter(brand => !brand.isImported);
    if (selectedBrands.size === availableBrands.length) {
      setSelectedBrands(new Set());
    } else {
      setSelectedBrands(new Set(availableBrands.map(brand => brand.id)));
    }
  };

  const handleImport = async () => {
    if (selectedBrands.size === 0) {
      setMessage('❌ Please select at least one brand to import');
      return;
    }

    setIsImporting(true);
    setMessage("");

    try {
      const response = await fetch('/api/sync/brands', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedBrandIds: Array.from(selectedBrands)
        }),
      });

      const result = await response.json();

      if (result.success) {
        setMessage(`✅ ${result.message}`);
        setSelectedBrands(new Set());

        // Refresh the brands list to update import status
        await fetchAvailableBrands();

        // Refresh the main page
        router.refresh();
      } else {
        setMessage(`❌ ${result.message}`);
      }
    } catch (error) {
      console.error('Error importing brands:', error);
      setMessage('❌ Failed to import brands');
    } finally {
      setIsImporting(false);
    }
  };

  const availableBrandsCount = filteredBrands.filter(brand => !brand.isImported).length;
  const selectedAvailableBrands = filteredBrands.filter(brand =>
    !brand.isImported && selectedBrands.has(brand.id)
  ).length;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Download className="mr-2 h-4 w-4" />
          Import from GSMArena
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Import Brands from GSMArena</DialogTitle>
          <DialogDescription>
            Select the brands you want to import from GSMArena. Already imported brands are marked and cannot be selected.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* Stats */}
          {stats.total > 0 && (
            <div className="flex gap-4 text-sm">
              <span>Total: <strong>{stats.total}</strong></span>
              <span>Available: <strong>{stats.available}</strong></span>
              <span>Already Imported: <strong>{stats.imported}</strong></span>
            </div>
          )}

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search brands..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Selection controls */}
          {!isLoading && filteredBrands.length > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={selectedAvailableBrands === availableBrandsCount && availableBrandsCount > 0}
                  onCheckedChange={handleSelectAll}
                  disabled={availableBrandsCount === 0}
                />
                <span className="text-sm">
                  Select all available ({selectedBrands.size} of {availableBrandsCount} selected)
                </span>
              </div>
            </div>
          )}

          {/* Brands table */}
          <div className="flex-1 overflow-auto border rounded-md">
            {isLoading ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                Loading brands...
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">Select</TableHead>
                    <TableHead>Brand Name</TableHead>
                    <TableHead>Devices</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredBrands.map((brand) => (
                    <TableRow key={brand.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedBrands.has(brand.id)}
                          onCheckedChange={() => handleBrandToggle(brand.id, brand.isImported)}
                          disabled={brand.isImported}
                        />
                      </TableCell>
                      <TableCell className="font-medium">{brand.name}</TableCell>
                      <TableCell>{brand.devices} devices</TableCell>
                      <TableCell>
                        {brand.isImported ? (
                          <Badge variant="secondary" className="flex items-center gap-1 w-fit">
                            <CheckCircle className="h-3 w-3" />
                            Imported
                          </Badge>
                        ) : (
                          <Badge variant="outline">Available</Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>

          {/* Message */}
          {message && (
            <p className="text-sm text-muted-foreground">{message}</p>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleImport}
            disabled={isImporting || selectedBrands.size === 0}
          >
            {isImporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Importing...
              </>
            ) : (
              `Import ${selectedBrands.size} Brand${selectedBrands.size !== 1 ? 's' : ''}`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
