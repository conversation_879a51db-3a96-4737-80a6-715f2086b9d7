"use client";

import { useEffect, useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { createBrandSchema, updateBrandSchema } from "@/schemas";
import { createBrand, updateBrand, getNextBrandPosition } from "@/actions/brand.actions";
import { ImageUpload } from "@/components/forms";
import { generateSlug } from "@/lib/utils";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

/**
 * Form component for creating or editing a brand
 */
export function BrandForm({ brand }) {
  const isEditMode = !!brand;
  const router = useRouter();

  // For transitions
  const [isPending, startTransition] = useTransition();

  // Form state
  const [formState, setFormState] = useState({
    success: false,
    message: '',
    isSubmitting: false
  });

  // State to store the next position
  const [nextPosition, setNextPosition] = useState(isEditMode ? brand.position : 1);

  // Initialize form with default values or brand data
  const form = useForm({
    resolver: zodResolver(isEditMode ? updateBrandSchema : createBrandSchema),
    defaultValues: {
      name: isEditMode ? brand.name : "",
      slug: isEditMode ? brand.slug : "",
      is_active: isEditMode ? brand.is_active : true,
      img: isEditMode ? (brand.img || "") : "",
      position: isEditMode ? brand.position : nextPosition,
      external_data: isEditMode ? (brand.external_data || {
        source: 'manual',
        external_id: '',
        last_synced: null,
      }) : {
        source: 'manual',
        external_id: '',
        last_synced: null,
      },
    },
  });

  // Fetch the next position when the component mounts (only for create mode)
  useEffect(() => {
    if (!isEditMode) {
      const fetchNextPosition = async () => {
        try {
          const nextPosition = await getNextBrandPosition();
          setNextPosition(nextPosition);
          // Update the form with the new position
          form.setValue("position", nextPosition);
        } catch (error) {
          console.error('Error fetching next position:', error);
          // Default to 1 if there's an error
          setNextPosition(1);
          form.setValue("position", 1);
        }
      };

      fetchNextPosition();
    }
  }, [form, isEditMode]);

  // Track if slug has been manually edited
  const [slugEdited, setSlugEdited] = useState(isEditMode);

  // Watch name field to auto-generate slug
  const name = form.watch("name");

  // Use useEffect to auto-generate slug when name changes
  useEffect(() => {
    if (name && !slugEdited) {
      const generatedSlug = generateSlug(name);
      form.setValue("slug", generatedSlug);
    }
  }, [name, slugEdited, form]);

  // Handle form submission
  const onSubmit = (values) => {
    setFormState({ ...formState, isSubmitting: true });

    startTransition(async () => {
      try {
        // Convert form values to the expected format
        const formValues = {
          name: values.name,
          slug: values.slug,
          position: parseInt(values.position, 10),
          is_active: values.is_active,
          img: values.img || '',
        };

        // Submit the form data to the server
        let result;
        if (isEditMode) {
          result = await updateBrand(brand._id, formValues);
        } else {
          result = await createBrand(formValues);
        }

        // Update form state with the result
        setFormState({
          success: result.success,
          message: result.message,
          isSubmitting: false
        });

        // If successful, redirect to the brand detail page
        if (result.success) {
          router.push(isEditMode ? `/dashboard/brands/${brand._id}` : '/dashboard/brands');
        }
      } catch (error) {
        // Handle errors
        setFormState({
          success: false,
          message: error.message || `Failed to ${isEditMode ? 'update' : 'create'} brand`,
          isSubmitting: false
        });
      }
    });
  };

  return (
    <Card>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardHeader>
            <CardTitle>{isEditMode ? 'Edit Brand' : 'Create New Brand'}</CardTitle>
            <CardDescription>
              {isEditMode ? 'Update brand information' : 'Add a new brand to your catalog'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              {/* Name field */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Brand Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Apple" {...field} />
                    </FormControl>
                    <FormDescription>
                      The name of the brand as it will appear to users.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Slug field */}
              <FormField
                control={form.control}
                name="slug"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Slug</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="apple"
                        {...field}
                        onChange={(e) => {
                          // If user manually edits the slug, mark it as edited
                          if (e.target.value !== generateSlug(name)) {
                            setSlugEdited(true);
                          }
                          field.onChange(e);
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      URL-friendly version of the name. Auto-generated but can be customized.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Position field */}
              <FormField
                control={form.control}
                name="position"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Position</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        placeholder="1"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value, 10) || 1)}
                      />
                    </FormControl>
                    <FormDescription>
                      Display order of the brand (lower numbers appear first).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Active status field */}
              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Status</FormLabel>
                      <FormDescription>
                        Whether this brand is active and visible to users.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Image field */}
              <FormField
                control={form.control}
                name="img"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Brand Image</FormLabel>
                    <FormControl>
                      <ImageUpload
                        value={field.value}
                        onChange={field.onChange}
                        uploadPath="brands"
                        placeholder="https://example.com/brand-logo.png"
                        description="Upload a logo or image for this brand. Recommended size: 200x200px."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div>
              {formState.message && (
                <p className={`text-sm ${formState.success ? 'text-primary' : 'text-destructive'}`}>
                  {formState.message}
                </p>
              )}
              {(isPending || formState.isSubmitting) && (
                <p className="text-sm text-accent">
                  Processing...
                </p>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                asChild
                disabled={isPending || formState.isSubmitting}
              >
                <a href={isEditMode ? `/dashboard/brands/${brand._id}` : '/dashboard/brands'}>
                  Cancel
                </a>
              </Button>
              <Button type="submit" disabled={isPending || formState.isSubmitting}>
                {(isPending || formState.isSubmitting)
                  ? (isEditMode ? 'Updating...' : 'Creating...')
                  : (isEditMode ? 'Update Brand' : 'Create Brand')
                }
              </Button>
            </div>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
