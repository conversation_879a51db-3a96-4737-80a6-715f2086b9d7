import { getDevices } from '@/actions/device.actions';
import { getBrands } from '@/actions/brand.actions';
import { getSeries } from '@/actions/series.actions';
import { PageHeader } from '@/components/layout';
import { FilterSection, BrandFilter, SeriesFilter } from '@/components/filters';
import { deviceColumns } from './columns';
import { PaginationHandler } from './pagination-handler';
import { DeviceEmptyState } from './_components/device-empty-state';

export const metadata = {
  title: 'Devices - PhoneNeed',
  description: 'Manage phone devices in your catalog',
};

export default async function DevicesPage(props) {
  // Parse search params for pagination and filtering
  const searchParams = await props.searchParams;
  const pageParam = Number(searchParams?.page) || 1;
  const sort = searchParams?.sort || 'position';
  const order = searchParams?.order || 'asc';
  const brandId = searchParams?.brand_id || '';
  const seriesId = searchParams?.series_id || '';

  // Ensure page is a valid number
  const page = isNaN(pageParam) ? 1 : pageParam;

  // Fetch all active brands for the filter
  const { brands } = await getBrands({
    is_active: true,
    limit: 100, // Get a reasonable number of brands
  });

  // Fetch all active series for the filter
  const { series } = await getSeries({
    is_active: true,
    brand_id: brandId, // Filter series by brand if a brand is selected
    limit: 100, // Get a reasonable number of series
  });

  // Fetch devices with filters
  const { devices, pagination } = await getDevices({
    page,
    sort,
    order,
    brand_id: brandId,
    series_id: seriesId,
    limit: 10
  });

  // Create a map of brand IDs to brand names for the data table
  const brandMap = brands.reduce((map, brand) => {
    map[brand._id] = brand.name;
    return map;
  }, {});

  // Create a map of series IDs to series names for the data table
  const seriesMap = series.reduce((map, series) => {
    map[series._id] = series.name;
    return map;
  }, {});

  // Add brand and series names to each device
  const devicesWithNames = devices.map(device => ({
    ...device,
    brand_name: brandMap[device.brand_id] || 'Unknown Brand',
    series_name: device.series_id ? (seriesMap[device.series_id] || 'Unknown Series') : null
  }));

  return (
    <div className="space-y-6">
      <PageHeader
        title="Devices"
        description="Manage phone devices in your catalog"
        actionHref="/dashboard/devices/new"
        actionLabel="Add Device"
      />

      {/* Filter Section */}
      <FilterSection
        defaultValues={{ brand_id: brandId, series_id: seriesId }}
      >
        <BrandFilter
          brands={brands}
          name="brand_id"
          label="Filter by Brand"
        />
        <SeriesFilter
          series={series}
          name="series_id"
          label="Filter by Series"
        />
      </FilterSection>

      {devicesWithNames.length === 0 ? (
        <DeviceEmptyState brandId={brandId} seriesId={seriesId} />
      ) : (
        <div className="space-y-4">
          <PaginationHandler
            columns={deviceColumns}
            data={devicesWithNames}
            pagination={pagination}
          />
        </div>
      )}
    </div>
  );
}
