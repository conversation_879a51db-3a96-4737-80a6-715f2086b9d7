"use client";

import { useEffect, useState, useTransition } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { createDeviceSchema, updateDeviceSchema } from "@/schemas";
import { createDevice, updateDevice, getDevices } from "@/actions/device.actions";
import { getBrands } from "@/actions/brand.actions";
import { getSeries } from "@/actions/series.actions";
import { ImageUpload } from "@/components/forms";
import { generateSlug } from "@/lib/utils";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

/**
 * Form component for creating or editing a device
 */
export function DeviceForm({ device }) {
  const isEditMode = !!device;
  const router = useRouter();
  const searchParams = useSearchParams();

  // For transitions
  const [isPending, startTransition] = useTransition();

  // Form state
  const [formState, setFormState] = useState({
    success: false,
    message: '',
    isSubmitting: false
  });

  // State to store brands and series
  const [brands, setBrands] = useState([]);
  const [seriesList, setSeriesList] = useState([]);

  // State to store the next position
  const [nextPosition, setNextPosition] = useState(isEditMode ? device?.position : 1);

  // Function to fetch the next position based on brand_id and series_id
  const fetchNextPosition = async (brandId, seriesId) => {
    if (!brandId) return;

    try {
      console.log(`Fetching next position for brand_id=${brandId} and series_id=${seriesId || 'null'}`);

      // Build query for finding devices with the same brand_id and series_id
      const query = { brand_id: brandId };

      // Add series_id to query if provided and not 'none'
      if (seriesId && seriesId !== 'none') {
        query.series_id = seriesId;
      } else if (seriesId === 'none') {
        // If series_id is 'none', find devices with null series_id
        query.series_id = null;
      }

      console.log('Query for next position:', query);

      // Fetch devices with the same brand_id and series_id
      const result = await getDevices(query);

      if (result && result.devices && result.devices.length > 0) {
        // Find the highest position
        const positions = result.devices.map(device => device.position);
        console.log('Found positions:', positions);

        const highestPosition = Math.max(...positions);
        console.log('Highest position:', highestPosition);

        setNextPosition(highestPosition + 1);

        // Update the position field in the form
        if (!isEditMode) {
          form.setValue('position', highestPosition + 1);
        }
      } else {
        // If no devices found, set position to 1
        console.log('No devices found, setting position to 1');
        setNextPosition(1);

        // Update the position field in the form
        if (!isEditMode) {
          form.setValue('position', 1);
        }
      }
    } catch (error) {
      console.error('Error fetching next position:', error);
      // Default to 1 if there's an error
      setNextPosition(1);

      // Update the position field in the form
      if (!isEditMode) {
        form.setValue('position', 1);
      }
    }
  };

  // Get brand_id and series_id from URL if available (for new device)
  const brandIdFromUrl = searchParams.get('brand_id');
  const seriesIdFromUrl = searchParams.get('series_id');

  // Initialize form with default values or device data
  const form = useForm({
    resolver: zodResolver(isEditMode ? updateDeviceSchema : createDeviceSchema),
    defaultValues: {
      name: isEditMode ? device.name : "",
      slug: isEditMode ? device.slug : "",
      brand_id: isEditMode ? device.brand_id : brandIdFromUrl || "",
      series_id: isEditMode ? (device.series_id || "none") : seriesIdFromUrl || "none",
      is_active: isEditMode ? device.is_active : true,
      img: isEditMode ? (device.img || "") : "",
      position: isEditMode ? device.position : nextPosition,
    },
  });

  // Fetch brands when the component mounts
  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const result = await getBrands({ is_active: true, limit: 100 });
        if (result.brands) {
          setBrands(result.brands);

          // If we have brands but no brand_id is selected, select the first brand
          const currentBrandId = form.getValues('brand_id');
          if (result.brands.length > 0 && (!currentBrandId || currentBrandId === '')) {
            form.setValue('brand_id', result.brands[0]._id);
          }

          // If we're in create mode and have a brand_id, fetch the next position
          if (!isEditMode) {
            const brandId = form.getValues('brand_id') || (result.brands.length > 0 ? result.brands[0]._id : null);
            const seriesId = form.getValues('series_id');

            if (brandId) {
              fetchNextPosition(brandId, seriesId);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching brands:', error);
        setFormState({
          success: false,
          message: 'Error loading brands. Please refresh the page.',
          isSubmitting: false
        });
      }
    };

    fetchBrands();
  }, [form, isEditMode]);

  // Fetch series when brand_id changes
  useEffect(() => {
    const brandId = form.getValues('brand_id');

    if (brandId) {
      const fetchSeries = async () => {
        try {
          const result = await getSeries({
            brand_id: brandId,
            is_active: true,
            limit: 100
          });

          if (result.series) {
            setSeriesList(result.series);

            // If we have a series_id that doesn't belong to this brand, reset it to "none"
            const currentSeriesId = form.getValues('series_id');
            if (currentSeriesId && currentSeriesId !== 'none') {
              const seriesExists = result.series.some(series => series._id === currentSeriesId);
              if (!seriesExists) {
                form.setValue('series_id', 'none');
              }
            }

            // Fetch next position based on brand_id and series_id
            if (!isEditMode) {
              fetchNextPosition(brandId, form.getValues('series_id'));
            }
          }
        } catch (error) {
          console.error('Error fetching series:', error);
          setFormState({
            success: false,
            message: 'Error loading series. Please try selecting a different brand.',
            isSubmitting: false
          });
          setSeriesList([]);
        }
      };

      fetchSeries();
    } else {
      setSeriesList([]);
      form.setValue('series_id', 'none');
    }
  }, [form, form.watch('brand_id'), isEditMode]);

  // Track if slug has been manually edited
  const [slugEdited, setSlugEdited] = useState(isEditMode);

  // Watch name field to auto-generate slug
  const name = form.watch("name");

  // Update position when series_id changes
  useEffect(() => {
    if (!isEditMode) {
      const brandId = form.getValues('brand_id');
      const seriesId = form.getValues('series_id');

      if (brandId) {
        fetchNextPosition(brandId, seriesId);
      }
    }
  }, [form, form.watch('series_id'), isEditMode]);

  // Use useEffect to auto-generate slug when name changes
  useEffect(() => {
    if (name && !slugEdited) {
      const generatedSlug = generateSlug(name);
      form.setValue("slug", generatedSlug);
    }
  }, [name, slugEdited, form]);

  // Handle form submission
  const onSubmit = (values) => {
    setFormState({ ...formState, isSubmitting: true, message: '' });

    startTransition(async () => {
      try {
        // Convert form values to the expected format
        const formValues = {
          name: values.name,
          slug: values.slug,
          brand_id: values.brand_id,
          series_id: values.series_id, // Let the server handle 'none' conversion
          position: parseInt(values.position, 10),
          is_active: values.is_active,
          img: values.img || '',
        };

        // Submit the form data to the server
        let result;
        if (isEditMode) {
          result = await updateDevice(device._id, formValues);
        } else {
          result = await createDevice(formValues);
        }

        // Update form state with the result
        setFormState({
          success: result.success,
          message: result.message,
          isSubmitting: false
        });

        // If successful and redirect is true, redirect to the specified URL
        if (result.success && result.redirect) {
          // Show success message briefly before redirecting
          setTimeout(() => {
            router.push(result.redirectUrl);
          }, 1000);
        } else if (result.success && !isEditMode) {
          // If successful but no redirect (create mode), reset the form
          form.reset({
            name: "",
            slug: "",
            brand_id: brandIdFromUrl || "",
            series_id: seriesIdFromUrl || "none",
            is_active: true,
            img: "",
            position: nextPosition + 1, // Increment position for next device
          });
          setSlugEdited(false);
        }
      } catch (error) {
        console.error('Error submitting form:', error);

        // Handle validation errors
        if (error.errors) {
          // Format Zod validation errors
          const errorMessages = Object.entries(error.errors)
            .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
            .join('; ');

          setFormState({
            success: false,
            message: `Validation error: ${errorMessages}`,
            isSubmitting: false
          });
        } else {
          setFormState({
            success: false,
            message: error.message || 'An error occurred while submitting the form',
            isSubmitting: false
          });
        }
      }
    });
  };

  return (
    <Card>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardHeader>
            <CardTitle>{isEditMode ? 'Edit Device' : 'Create New Device'}</CardTitle>
            <CardDescription>
              {isEditMode ? 'Update device information' : 'Add a new device to your catalog'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              {/* Brand field */}
              <FormField
                control={form.control}
                name="brand_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Brand</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        // Reset series_id when brand changes
                        if (value !== field.value) {
                          form.setValue("series_id", "none");
                        }
                      }}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a brand" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {brands.map((brand) => (
                          <SelectItem key={brand._id} value={brand._id}>
                            {brand.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The brand this device belongs to.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Series field */}
              <FormField
                control={form.control}
                name="series_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Series (Optional)</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a series (optional)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        {seriesList.map((series) => (
                          <SelectItem key={series._id} value={series._id}>
                            {series.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The series this device belongs to (if applicable).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Name field */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Device Name</FormLabel>
                    <FormControl>
                      <Input placeholder="iPhone 16 Pro Max" {...field} />
                    </FormControl>
                    <FormDescription>
                      The name of the device as it will appear to users.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Slug field */}
              <FormField
                control={form.control}
                name="slug"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Slug</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="iphone-16-pro-max"
                        {...field}
                        onChange={(e) => {
                          // If user manually edits the slug, mark it as edited
                          if (e.target.value !== generateSlug(name)) {
                            setSlugEdited(true);
                          }
                          field.onChange(e);
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      URL-friendly version of the name. Must be unique. Auto-generated but can be customized.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Position field */}
              <FormField
                control={form.control}
                name="position"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Position</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        placeholder="1"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value, 10) || 1)}
                      />
                    </FormControl>
                    <FormDescription>
                      Display order of the device (lower numbers appear first).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Active status field */}
              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Status</FormLabel>
                      <FormDescription>
                        Inactive devices will not be visible to users.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Image field */}
              <FormField
                control={form.control}
                name="img"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Device Image</FormLabel>
                    <FormControl>
                      <ImageUpload
                        value={field.value}
                        onChange={field.onChange}
                        uploadPath="devices"
                        placeholder="https://example.com/device-image.png"
                        description="Upload an image for this device. Recommended size: 200x200px."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <div className="w-full">
              {formState.message && (
                <div className={`p-3 rounded-md mb-4 ${formState.success ? 'bg-primary/10 text-primary' : 'bg-destructive/10 text-destructive'}`}>
                  <p className="text-sm font-medium">
                    {formState.message}
                  </p>
                </div>
              )}
            </div>
            <div className="flex justify-between w-full">
              <div>
                {(isPending || formState.isSubmitting) && (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                    <p className="text-sm text-muted-foreground">
                      {isEditMode ? 'Updating device...' : 'Creating device...'}
                    </p>
                  </div>
                )}
              </div>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={isPending || formState.isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isPending || formState.isSubmitting}
                  className={isPending || formState.isSubmitting ? 'opacity-70' : ''}
                >
                  {isPending || formState.isSubmitting
                    ? (isEditMode ? 'Updating...' : 'Creating...')
                    : (isEditMode ? 'Update Device' : 'Create Device')}
                </Button>
              </div>
            </div>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
