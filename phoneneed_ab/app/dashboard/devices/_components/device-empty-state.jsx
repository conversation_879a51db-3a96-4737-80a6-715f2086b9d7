import { Button } from "@/components/ui/button";
import { Smartphone } from "lucide-react";
import Link from "next/link";

/**
 * Empty state component for when no devices are found
 */
export function DeviceEmptyState({ brandId, seriesId }) {
  // Determine the appropriate action URL based on the filters
  const actionUrl = seriesId 
    ? `/dashboard/devices/new?series_id=${seriesId}${brandId ? `&brand_id=${brandId}` : ''}`
    : brandId 
      ? `/dashboard/devices/new?brand_id=${brandId}` 
      : '/dashboard/devices/new';

  // Determine the message based on the filters
  let message = 'No devices found.';
  if (brandId && seriesId) {
    message = 'No devices found for this brand and series.';
  } else if (brandId) {
    message = 'No devices found for this brand.';
  } else if (seriesId) {
    message = 'No devices found for this series.';
  }

  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <Smartphone className="h-12 w-12 text-muted-foreground mb-4" />
      <h3 className="text-lg font-medium">No Devices Found</h3>
      <p className="text-sm text-muted-foreground mt-1 mb-4">
        {message}
      </p>
      <Button asChild>
        <Link href={actionUrl}>
          Add Device
        </Link>
      </Button>
    </div>
  );
}
