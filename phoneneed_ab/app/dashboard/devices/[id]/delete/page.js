import { getDeviceById } from '@/actions/device.actions';
import { DeleteConfirmation } from '@/components/forms';
import { PageHeader } from '@/components/layout';
import { notFound } from 'next/navigation';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export const metadata = {
  title: 'Delete Device - PhoneNeed',
  description: 'Confirm device deletion',
};

export default async function DeleteDevicePage({ params }) {
  const { id } = params;

  try {
    // Check if the ID is a valid MongoDB ObjectId format
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      console.error(`Invalid device ID format: ${id}`);
      notFound();
    }

    // Connect to the database directly to check if the device exists
    const { db } = await connectToDatabase();

    // Use a try-catch block to handle potential MongoDB errors
    let device;
    try {
      const objectId = new ObjectId(id);
      device = await db.collection('devices').findOne({ _id: objectId });

      if (!device) {
        console.error(`Device not found with ID: ${id}`);
        notFound();
      }

      // Convert MongoDB object to plain object
      device = {
        _id: device._id.toString(),
        name: device.name,
        slug: device.slug,
        brand_id: device.brand_id,
        series_id: device.series_id,
        is_active: device.is_active,
        img: device.img,
        position: device.position,
        created_at: device.created_at,
        updated_at: device.updated_at
      };
    } catch (error) {
      console.error(`Error fetching device with ID ${id}:`, error);
      notFound();
    }

    return (
      <div className="space-y-6">
        <PageHeader
          title="Delete Device"
          description="Confirm device deletion"
          backHref={`/dashboard/devices/${id}`}
          backLabel="Back to Device"
        />

        <DeleteConfirmation
          itemId={id}
          itemName={device.name}
          itemType="device"
          deleteAction="deleteDevice"
          returnPath="/dashboard/devices"
        />
      </div>
    );
  } catch (error) {
    console.error('Error loading device for deletion:', error);
    notFound();
  }
}
