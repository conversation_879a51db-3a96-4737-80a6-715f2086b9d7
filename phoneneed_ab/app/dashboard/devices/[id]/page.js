import { getDeviceById } from '@/actions/device.actions';
import { getBrandById } from '@/actions/brand.actions';
import { getSeriesById } from '@/actions/series.actions';
import { notFound } from 'next/navigation';
import { PageHeader } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { formatDate } from '@/lib/utils';
import { Pencil, Trash2 } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { EntityDetails } from '@/components/data-display';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export const metadata = {
  title: 'Device Details - PhoneNeed',
  description: 'View device details',
};

export default async function DeviceDetailPage({ params }) {
  const { id } = params;

  try {
    // Check if the ID is a valid MongoDB ObjectId format
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      console.error(`Invalid device ID format: ${id}`);
      notFound();
    }

    // Connect to the database directly to check if the device exists
    const { db } = await connectToDatabase();

    // Use a try-catch block to handle potential MongoDB errors
    let device;
    try {
      const objectId = new ObjectId(id);
      device = await db.collection('devices').findOne({ _id: objectId });

      if (!device) {
        console.error(`Device not found with ID: ${id}`);
        notFound();
      }

      // Convert MongoDB object to plain object
      device = {
        _id: device._id.toString(),
        name: device.name,
        slug: device.slug,
        brand_id: device.brand_id,
        series_id: device.series_id,
        is_active: device.is_active,
        img: device.img,
        position: device.position,
        created_at: device.created_at,
        updated_at: device.updated_at
      };
    } catch (error) {
      console.error(`Error fetching device with ID ${id}:`, error);
      notFound();
    }

    // Fetch brand details
    let brand;
    try {
      brand = await getBrandById(device.brand_id);
    } catch (error) {
      console.error(`Error fetching brand with ID ${device.brand_id}:`, error);
      // Continue with a placeholder brand
      brand = { _id: device.brand_id, name: 'Unknown Brand' };
    }

    // Fetch series details if available
    let series = null;
    if (device.series_id) {
      try {
        series = await getSeriesById(device.series_id);
      } catch (error) {
        console.error(`Error fetching series with ID ${device.series_id}:`, error);
        // Continue without series data
      }
    }

    return (
      <div className="space-y-6">
        <PageHeader
          title={device.name}
          description="Device details"
          backHref="/dashboard/devices"
          backLabel="Back to Devices"
          actions={
            <>
              <Button variant="outline" asChild>
                <Link href={`/dashboard/devices/${id}/edit`}>
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </Button>
              <Button variant="destructive" asChild>
                <Link href={`/dashboard/devices/${id}/delete`}>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </Link>
              </Button>
            </>
          }
        />

        <div className="grid gap-6 md:grid-cols-2">
          {/* Device Details */}
          <Card>
            <CardHeader>
              <CardTitle>Device Information</CardTitle>
              <CardDescription>Basic information about this device</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <EntityDetails
                fields={[
                  { label: 'Name', value: device.name },
                  { label: 'Slug', value: device.slug },
                  {
                    label: 'Brand',
                    value: (
                      <Link href={`/dashboard/brands/${brand._id}`} className="text-primary hover:underline">
                        {brand.name}
                      </Link>
                    )
                  },
                  {
                    label: 'Series',
                    value: series ? (
                      <Link href={`/dashboard/series/${series._id}`} className="text-primary hover:underline">
                        {series.name}
                      </Link>
                    ) : 'None'
                  },
                  { label: 'Status', value: device.is_active ? 'Active' : 'Inactive' },
                  { label: 'Position', value: device.position },
                  { label: 'Created', value: formatDate(device.created_at) },
                  { label: 'Last Updated', value: formatDate(device.updated_at) },
                ]}
              />
            </CardContent>
          </Card>

          {/* Device Image */}
          <Card>
            <CardHeader>
              <CardTitle>Device Image</CardTitle>
              <CardDescription>Preview of the device image</CardDescription>
            </CardHeader>
            <CardContent className="flex items-center justify-center p-6">
              {device.img ? (
                <div className="relative h-48 w-48 rounded-md overflow-hidden">
                  <Image
                    src={device.img}
                    alt={device.name}
                    fill
                    className="object-contain"
                  />
                </div>
              ) : (
                <div className="h-48 w-48 rounded-md bg-muted flex items-center justify-center text-muted-foreground">
                  No image available
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error loading device details:', error);
    notFound();
  }
}
