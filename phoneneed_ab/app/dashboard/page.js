import { connectToDatabase } from '@/lib/mongodb';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Smartphone, Layers, Tag, FolderTree } from 'lucide-react';
import Link from 'next/link';
import { Button } from "@/components/ui/button";
import { PageHeader } from '@/components/layout';

export const metadata = {
  title: 'Dashboard - PhoneNeed',
};

async function getStats() {
  try {
    const { db } = await connectToDatabase();

    const brandCount = await db.collection('brands').countDocuments();
    const seriesCount = await db.collection('series').countDocuments();
    const deviceCount = await db.collection('devices').countDocuments();
    const categoryCount = await db.collection('categories').countDocuments();

    return {
      brandCount,
      seriesCount,
      deviceCount,
      categoryCount,
    };
  } catch (error) {
    console.error('Error fetching stats:', error);
    return {
      brandCount: 0,
      seriesCount: 0,
      deviceCount: 0,
      categoryCount: 0,
    };
  }
}

export default async function DashboardPage() {
  const stats = await getStats();

  return (
    <div className="space-y-6">
      <PageHeader
        title="Dashboard"
        description="Overview of your phone catalog management system."
      >
        <Link href="/">
          <Button variant="outline">Back to Home</Button>
        </Link>
      </PageHeader>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Link href="/dashboard/brands">
          <Card className="hover:bg-muted/50 transition-colors cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">Total Brands</CardTitle>
              <Tag className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.brandCount}</div>
              <p className="text-xs text-muted-foreground">
                Phone brands in your catalog
              </p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/dashboard/series">
          <Card className="hover:bg-muted/50 transition-colors cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">Total Series</CardTitle>
              <Layers className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.seriesCount}</div>
              <p className="text-xs text-muted-foreground">
                Phone series across all brands
              </p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/dashboard/devices">
          <Card className="hover:bg-muted/50 transition-colors cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">Total Devices</CardTitle>
              <Smartphone className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.deviceCount}</div>
              <p className="text-xs text-muted-foreground">
                Phone devices in your catalog
              </p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/dashboard/categories">
          <Card className="hover:bg-muted/50 transition-colors cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">Total Categories</CardTitle>
              <FolderTree className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.categoryCount}</div>
              <p className="text-xs text-muted-foreground">
                Product categories in your catalog
              </p>
            </CardContent>
          </Card>
        </Link>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks you might want to perform
            </CardDescription>
          </CardHeader>
          <CardContent className="grid gap-2">
            <Link
              href="/dashboard/brands/new"
              className="flex items-center p-2 rounded-md hover:bg-muted transition-colors"
            >
              <Tag className="h-4 w-4 mr-2 text-primary" />
              <span>Add New Brand</span>
            </Link>
            <Link
              href="/dashboard/series/new"
              className="flex items-center p-2 rounded-md hover:bg-muted transition-colors"
            >
              <Layers className="h-4 w-4 mr-2 text-primary" />
              <span>Add New Series</span>
            </Link>
            <Link
              href="/dashboard/devices/new"
              className="flex items-center p-2 rounded-md hover:bg-muted transition-colors"
            >
              <Smartphone className="h-4 w-4 mr-2 text-primary" />
              <span>Add New Device</span>
            </Link>
            <Link
              href="/dashboard/categories/new"
              className="flex items-center p-2 rounded-md hover:bg-muted transition-colors"
            >
              <FolderTree className="h-4 w-4 mr-2 text-primary" />
              <span>Add New Category</span>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Getting Started</CardTitle>
            <CardDescription>
              Tips for managing your phone catalog
            </CardDescription>
          </CardHeader>
          <CardContent className="grid gap-2">
            <div className="text-sm">
              <p className="mb-2">
                To effectively manage your phone catalog, follow these steps:
              </p>
              <ol className="list-decimal list-inside space-y-1">
                <li>Create brands (e.g., Apple, Samsung)</li>
                <li>Add series for each brand (e.g., iPhone, Galaxy S)</li>
                <li>Add devices to each series (e.g., iPhone 16 Pro Max)</li>
                <li>Create categories to organize products (e.g., Accessories, Cases)</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
