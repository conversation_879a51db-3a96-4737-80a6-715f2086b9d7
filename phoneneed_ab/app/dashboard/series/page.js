import { getSeries, getSeriesByBrandId } from '@/actions/series.actions';
import { getBrands } from '@/actions/brand.actions';
import { PageHeader } from '@/components/layout';
import { FilterSection, BrandFilter } from '@/components/filters';
import { seriesColumns } from './columns';
import { PaginationHandler } from './pagination-handler';
import { SeriesEmptyState } from './_components/series-empty-state';

export const metadata = {
  title: 'Series - PhoneNeed',
  description: 'Manage phone series in your catalog',
};

export default async function SeriesPage(props) {
  // Parse search params for pagination and filtering
  const searchParams = await props.searchParams;
  const pageParam = Number(searchParams?.page) || 1;
  const sort = searchParams?.sort || 'position';
  const order = searchParams?.order || 'asc';
  const brandId = searchParams?.brand_id || '';

  // Ensure page is a valid number
  const page = isNaN(pageParam) ? 1 : pageParam;

  // Fetch all active brands for the filter
  const { brands } = await getBrands({
    is_active: true,
    limit: 100, // Get a reasonable number of brands
  });

  // Determine whether to fetch all series or filter by brand
  let series = [];
  let pagination = { total: 0, page, limit: 10, pages: 1 };

  if (brandId) {
    // If a brand is selected, get all series for that brand
    const seriesResult = await getSeriesByBrandId(brandId, true); // Include inactive series

    console.log('Series by brand result:', seriesResult);
    console.log('Is array?', Array.isArray(seriesResult));

    // Make sure we have an array
    series = Array.isArray(seriesResult) ? seriesResult : [];
    if (!Array.isArray(series)) {
      console.error('Series is not an array after check:', series);
      series = []; // Fallback to empty array
    }

    // Create pagination object for consistency
    pagination = {
      total: series.length,
      page: 1,
      limit: series.length || 1,
      pages: 1
    };
  } else {
    // Otherwise, get paginated series
    const result = await getSeries({
      page,
      sort,
      order,
      limit: 10
    });

    console.log('Get series result:', result);
    console.log('Is series array?', Array.isArray(result.series));

    // Make sure we have an array
    series = Array.isArray(result.series) ? result.series : [];
    if (!Array.isArray(series)) {
      console.error('Series is not an array after check:', series);
      series = []; // Fallback to empty array
    }

    pagination = result.pagination;
  }

  console.log('Series before brand mapping:', series);

  // Create a map of brand IDs to brand names for the data table
  const brandMap = brands.reduce((map, brand) => {
    map[brand._id] = brand.name;
    return map;
  }, {});

  // Add brand name to each series
  try {
    console.log('Series type:', typeof series);
    console.log('Is array?', Array.isArray(series));

    if (!Array.isArray(series)) {
      console.error('Series is not an array:', series);
      series = [];
    } else {
      series = series.map(item => ({
        ...item,
        brand_name: brandMap[item.brand_id] || 'Unknown Brand'
      }));
    }
  } catch (error) {
    console.error('Error mapping series:', error);
    series = [];
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Series"
        description="Manage phone series in your catalog"
        actionHref="/dashboard/series/new"
        actionLabel="Add Series"
      />

      {/* Filter Section */}
      <FilterSection
        defaultValues={{ brand_id: brandId }}
      >
        <BrandFilter
          brands={brands}
          name="brand_id"
          label="Filter by Brand"
        />
      </FilterSection>

      {series.length === 0 ? (
        <SeriesEmptyState brandId={brandId} />
      ) : (
        <div className="space-y-4">
          <PaginationHandler
            columns={seriesColumns}
            data={series}
            pagination={pagination}
          />
        </div>
      )}
    </div>
  );
}
