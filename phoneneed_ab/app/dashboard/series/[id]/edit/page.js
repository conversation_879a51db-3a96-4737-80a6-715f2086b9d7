import { getSeriesById } from '@/actions/series.actions';
import { notFound } from 'next/navigation';
import { SeriesForm } from '../../_components/series-form';
import { PageHeader } from '@/components/layout';

export const metadata = {
  title: 'Edit Series - PhoneNeed',
  description: 'Edit series details',
};

export default async function EditSeriesPage(props) {
  try {
    // Await the params object before accessing its properties
    const params = await props.params;
    const id = params.id;

    // Fetch series details
    const series = await getSeriesById(id);

    if (!series) {
      notFound();
    }

    return (
      <div className="space-y-6">
        <PageHeader
          title="Edit Series"
          description="Update series information"
          backHref={`/dashboard/series/${id}`}
          backLabel="Back to Series Details"
        />

        <SeriesForm series={series} />
      </div>
    );
  } catch (error) {
    console.error('Error loading series for editing:', error);
    notFound();
  }
}
