import { getSeriesById } from '@/actions/series.actions';
import { getBrandById } from '@/actions/brand.actions';
import { getDevicesBySeriesId } from '@/actions/device.actions';
import { notFound } from 'next/navigation';
import { PageHeader } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { formatDate } from '@/lib/utils';
import { Pencil, Trash2, Smartphone } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

export const metadata = {
  title: 'Series Details - PhoneNeed',
  description: 'View series details',
};

export default async function SeriesDetailPage(props) {
  try {
    // Await the params object before accessing its properties
    const params = await props.params;
    const id = params.id;

    // Fetch series details
    let series;
    try {
      series = await getSeriesById(id);
    } catch (error) {
      console.error(`Error fetching series with ID ${id}:`, error);
      notFound();
    }

    // Fetch brand details
    let brand;
    try {
      brand = await getBrandById(series.brand_id);
    } catch (error) {
      console.error(`Error fetching brand with ID ${series.brand_id}:`, error);
      // Continue without brand data
      brand = { name: 'Unknown Brand' };
    }

    // Fetch devices in this series
    let devices = [];
    try {
      devices = await getDevicesBySeriesId(id);
    } catch (error) {
      console.error(`Error fetching devices for series ${id}:`, error);
      // Continue with empty devices array
    }

    return (
      <div className="space-y-6">
        <PageHeader
          title={series.name}
          description="Series details"
          backHref="/dashboard/series"
          backLabel="Back to Series"
          actions={
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/dashboard/series/${id}/edit`}>
                  <Pencil className="h-4 w-4 mr-2" />
                  Edit
                </Link>
              </Button>
            </div>
          }
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Series Information */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Series Information</CardTitle>
              <CardDescription>Details about this series</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Name</p>
                  <p>{series.name}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Slug</p>
                  <p>{series.slug}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Brand</p>
                  <p>
                    <Link href={`/dashboard/brands/${series.brand_id}`} className="text-primary hover:underline">
                      {brand?.name || 'Unknown Brand'}
                    </Link>
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Position</p>
                  <p>{series.position}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Status</p>
                  <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    series.is_active
                      ? "bg-primary text-primary-foreground"
                      : "bg-destructive text-destructive-foreground"
                  }`}>
                    {series.is_active ? "Active" : "Inactive"}
                  </div>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Created</p>
                  <p>{formatDate(series.created_at)}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                  <p>{formatDate(series.updated_at)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Series Image */}
          <Card>
            <CardHeader>
              <CardTitle>Series Image</CardTitle>
              <CardDescription>Preview of the series image</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              {series.img ? (
                <div className="relative h-48 w-48 rounded-md overflow-hidden">
                  <Image
                    src={series.img}
                    alt={series.name}
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className="h-48 w-48 rounded-md bg-muted flex items-center justify-center text-muted-foreground">
                  <Smartphone className="h-12 w-12" />
                  <p className="mt-2">No image</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Devices Section */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Devices</CardTitle>
              <CardDescription>Devices in this series</CardDescription>
            </div>
            <Button asChild>
              <Link href={`/dashboard/devices/new?series_id=${id}&brand_id=${series.brand_id}`}>
                Add Device
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            {devices.length === 0 ? (
              <div className="text-center py-6 text-muted-foreground">
                <Smartphone className="h-12 w-12 mx-auto mb-4" />
                <p>No devices found in this series.</p>
                <p className="mt-2">Add a device to get started.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {devices.map((device) => (
                  <Link
                    key={device._id}
                    href={`/dashboard/devices/${device._id}`}
                    className="block group"
                  >
                    <div className="border rounded-md p-4 hover:border-primary transition-colors">
                      <div className="flex items-center space-x-4">
                        {device.img ? (
                          <div className="relative h-12 w-12 rounded-md overflow-hidden flex-shrink-0">
                            <Image
                              src={device.img}
                              alt={device.name}
                              fill
                              className="object-cover"
                            />
                          </div>
                        ) : (
                          <div className="h-12 w-12 rounded-md bg-muted flex items-center justify-center flex-shrink-0">
                            <Smartphone className="h-6 w-6 text-muted-foreground" />
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm group-hover:text-primary transition-colors truncate">
                            {device.name}
                          </h4>
                          <p className="text-xs text-muted-foreground truncate">
                            {device.is_active ? 'Active' : 'Inactive'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  } catch (error) {
    console.error('Error loading series details:', error);
    notFound();
  }
}
