"use client";

import { EmptyState } from "@/components/data-display";
import { Smartphone } from "lucide-react";

export function SeriesEmptyState({ brandId }) {
  return (
    <EmptyState
      title="No series found"
      description={
        brandId
          ? "No series found for the selected brand. Try changing your filters or create a new series."
          : "Get started by creating your first series."
      }
      icon={Smartphone}
      actionHref={
        brandId
          ? `/dashboard/series/new?brand_id=${brandId}`
          : "/dashboard/series/new"
      }
      actionLabel="Add Series"
    />
  );
}
