"use client";

import { useEffect, useState, useTransition } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { createSeriesSchema, updateSeriesSchema } from "@/schemas";
import { createSeries, updateSeries, getNextSeriesPosition } from "@/actions/series.actions";
import { getBrands } from "@/actions/brand.actions";
import { ImageUpload } from "@/components/forms";
import { generateSlug } from "@/lib/utils";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

/**
 * Form component for creating or editing a series
 */
export function SeriesForm({ series }) {
  const isEditMode = !!series;
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // For transitions
  const [isPending, startTransition] = useTransition();
  
  // Form state
  const [formState, setFormState] = useState({ 
    success: false, 
    message: '', 
    isSubmitting: false 
  });

  // State to store brands
  const [brands, setBrands] = useState([]);
  
  // State to store the next position
  const [nextPosition, setNextPosition] = useState(isEditMode ? series?.position : 1);

  // Get brand_id from URL if available (for new series)
  const brandIdFromUrl = searchParams.get('brand_id');

  // Initialize form with default values or series data
  const form = useForm({
    resolver: zodResolver(isEditMode ? updateSeriesSchema : createSeriesSchema),
    defaultValues: {
      name: isEditMode ? series.name : "",
      slug: isEditMode ? series.slug : "",
      brand_id: isEditMode ? series.brand_id : brandIdFromUrl || "",
      is_active: isEditMode ? series.is_active : true,
      img: isEditMode ? (series.img || "") : "",
      position: isEditMode ? series.position : nextPosition,
    },
  });

  // Fetch brands when the component mounts
  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const result = await getBrands({ is_active: true, limit: 100 });
        if (result.brands) {
          setBrands(result.brands);
        }
      } catch (error) {
        console.error('Error fetching brands:', error);
      }
    };

    fetchBrands();
  }, []);

  // Fetch the next position when the component mounts or brand_id changes
  useEffect(() => {
    const brandId = form.getValues('brand_id');
    
    if (!isEditMode && brandId) {
      const fetchNextPosition = async () => {
        const result = await getNextSeriesPosition(brandId);
        if (result.success) {
          setNextPosition(result.nextPosition);
          // Update the form with the new position
          form.setValue("position", result.nextPosition);
        }
      };

      fetchNextPosition();
    }
  }, [form, isEditMode, form.watch('brand_id')]);

  // Track if slug has been manually edited
  const [slugEdited, setSlugEdited] = useState(isEditMode);

  // Watch name field to auto-generate slug
  const name = form.watch("name");

  // Use useEffect to auto-generate slug when name changes
  useEffect(() => {
    if (name && !slugEdited) {
      const generatedSlug = generateSlug(name);
      form.setValue("slug", generatedSlug);
    }
  }, [name, slugEdited, form]);

  // Handle form submission
  const onSubmit = (values) => {
    setFormState({ ...formState, isSubmitting: true });
    
    startTransition(async () => {
      try {
        // Convert form values to the expected format
        const formValues = {
          name: values.name,
          slug: values.slug,
          brand_id: values.brand_id,
          position: parseInt(values.position, 10),
          is_active: values.is_active,
          img: values.img || '',
        };
        
        // Submit the form data to the server
        let result;
        if (isEditMode) {
          result = await updateSeries(series._id, formValues);
        } else {
          result = await createSeries(formValues);
        }
        
        // Update form state with the result
        setFormState({
          success: result.success,
          message: result.message,
          isSubmitting: false
        });
        
        // If successful and redirect is true, redirect to the specified URL
        if (result.success && result.redirect) {
          router.push(result.redirectUrl);
        }
      } catch (error) {
        // Handle errors
        setFormState({
          success: false,
          message: error.message || `Failed to ${isEditMode ? 'update' : 'create'} series`,
          isSubmitting: false
        });
      }
    });
  };

  return (
    <Card>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardHeader>
            <CardTitle>{isEditMode ? 'Edit Series' : 'Create New Series'}</CardTitle>
            <CardDescription>
              {isEditMode ? 'Update series information' : 'Add a new series to your catalog'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              {/* Brand field */}
              <FormField
                control={form.control}
                name="brand_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Brand</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a brand" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {brands.map((brand) => (
                          <SelectItem key={brand._id} value={brand._id}>
                            {brand.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The brand this series belongs to.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Name field */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Series Name</FormLabel>
                    <FormControl>
                      <Input placeholder="iPhone" {...field} />
                    </FormControl>
                    <FormDescription>
                      The name of the series as it will appear to users.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Slug field */}
              <FormField
                control={form.control}
                name="slug"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Slug</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="iphone"
                        {...field}
                        onChange={(e) => {
                          // If user manually edits the slug, mark it as edited
                          if (e.target.value !== generateSlug(name)) {
                            setSlugEdited(true);
                          }
                          field.onChange(e);
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      URL-friendly version of the name. Auto-generated but can be customized.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Position field */}
              <FormField
                control={form.control}
                name="position"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Position</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        placeholder="1"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value, 10) || 1)}
                      />
                    </FormControl>
                    <FormDescription>
                      Display order of the series (lower numbers appear first).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Active status field */}
              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Status</FormLabel>
                      <FormDescription>
                        Whether this series is active and visible to users.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Image field */}
              <FormField
                control={form.control}
                name="img"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Series Image</FormLabel>
                    <FormControl>
                      <ImageUpload
                        value={field.value}
                        onChange={field.onChange}
                        uploadPath="series"
                        placeholder="https://example.com/series-image.png"
                        description="Upload an image for this series. Recommended size: 200x200px."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div>
              {formState.message && (
                <p className={`text-sm ${formState.success ? 'text-primary' : 'text-destructive'}`}>
                  {formState.message}
                </p>
              )}
              {(isPending || formState.isSubmitting) && (
                <p className="text-sm text-accent">
                  Processing...
                </p>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                asChild
                disabled={isPending || formState.isSubmitting}
              >
                <a href={isEditMode ? `/dashboard/series/${series._id}` : '/dashboard/series'}>
                  Cancel
                </a>
              </Button>
              <Button type="submit" disabled={isPending || formState.isSubmitting}>
                {(isPending || formState.isSubmitting)
                  ? (isEditMode ? 'Updating...' : 'Creating...')
                  : (isEditMode ? 'Update Series' : 'Create Series')
                }
              </Button>
            </div>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
