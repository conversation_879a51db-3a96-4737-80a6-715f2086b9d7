"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { DataTable } from "@/components/data-display";
import { useState } from "react";
import { bulkDeleteSeries } from "@/actions/series.actions";
import { toast } from "sonner";

export function PaginationHandler({ columns, data, pagination }) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isDeleting, setIsDeleting] = useState(false);
  const [shouldResetSelection, setShouldResetSelection] = useState(false);

  // Handle pagination changes
  const handlePaginationChange = ({ page }) => {
    // Create a new URLSearchParams instance
    const params = new URLSearchParams(searchParams);

    // Update the page parameter
    params.set("page", String(page));

    // Update the URL with the new search params
    router.push(`${pathname}?${params.toString()}`);
  };

  // Handle bulk delete
  const handleBulkDelete = async (selectedRows) => {
    if (!selectedRows.length) return;

    const seriesIds = selectedRows.map(row => row._id);
    const seriesNames = selectedRows.map(row => row.name).join(", ");

    if (confirm(`Are you sure you want to delete the following series: ${seriesNames}?`)) {
      setIsDeleting(true);
      setShouldResetSelection(true);

      try {
        const result = await bulkDeleteSeries(seriesIds);

        if (result.success) {
          toast.success(result.message);
          router.refresh();
        } else {
          toast.error(result.message);
        }
      } catch (error) {
        toast.error("An error occurred while deleting series");
        console.error(error);
      } finally {
        setIsDeleting(false);
        // Reset the shouldResetSelection flag after a short delay
        setTimeout(() => {
          setShouldResetSelection(false);
        }, 100);
      }
    }
  };

  return (
    <DataTable
      columns={columns}
      data={data}
      pagination={pagination}
      onPaginationChange={handlePaginationChange}
      onBulkDelete={handleBulkDelete}
      isDeleting={isDeleting}
      resetSelection={shouldResetSelection}
    />
  );
}
