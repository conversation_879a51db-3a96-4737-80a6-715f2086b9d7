"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { useSession, signOut } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  Sidebar,
  SidebarProvider,
  SidebarTrigger,
  SidebarContent,
  SidebarHeader,
  SidebarFooter,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";
import { LogOut } from "lucide-react";

// Import navigation items from config
import { mainNavItems, productNavItems, accountNavItems } from "@/config/navigation";

export default function DashboardLayout({ children }) {
  const { data: session } = useSession();
  const pathname = usePathname();

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex h-screen bg-background">
        <Sidebar>
          <SidebarHeader>
            <div className="flex items-center gap-2 px-2">
              <SidebarTrigger />
              <div className="flex flex-col">
                <h2 className="text-lg font-bold text-primary">PhoneNeed</h2>
                <p className="text-xs text-muted-foreground">
                  {session?.user?.name || session?.user?.email || "User"}
                </p>
              </div>
            </div>
          </SidebarHeader>

          <SidebarContent>
            {/* Main Navigation */}
            <SidebarMenu>
              {mainNavItems.map((item, index) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={pathname === item.href}
                    tooltip={item.title}
                    index={index}
                  >
                    <Link href={item.href}>
                      <item.icon className={cn(
                        "mr-2 transition-transform duration-300",
                        pathname === item.href ? "rotate-3 scale-110" : ""
                      )} />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>

            {/* Product Navigation */}
            {productNavItems.length > 0 && (
              <>
                <div className="px-3 py-2">
                  <h3 className="text-xs font-medium text-muted-foreground">Products</h3>
                </div>
                <SidebarMenu>
                  {productNavItems.map((item, index) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        asChild
                        isActive={pathname === item.href}
                        tooltip={item.title}
                        index={index + mainNavItems.length} // Continue index from main nav
                      >
                        <Link href={item.href}>
                          <item.icon className={cn(
                            "mr-2 transition-transform duration-300",
                            pathname === item.href ? "rotate-3 scale-110" : ""
                          )} />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </>
            )}

            {/* Account Navigation */}
            {accountNavItems.length > 0 && (
              <>
                <div className="px-3 py-2">
                  <h3 className="text-xs font-medium text-muted-foreground">Account</h3>
                </div>
                <SidebarMenu>
                  {accountNavItems.map((item, index) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        asChild
                        isActive={pathname === item.href}
                        tooltip={item.title}
                        index={index + mainNavItems.length + productNavItems.length} // Continue index from previous sections
                      >
                        <Link href={item.href}>
                          <item.icon className={cn(
                            "mr-2 transition-transform duration-300",
                            pathname === item.href ? "rotate-3 scale-110" : ""
                          )} />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </>
            )}
          </SidebarContent>

          <SidebarFooter>
            <Button
              variant="outline"
              className="w-full justify-start gap-2 transition-all duration-300 ease-in-out hover:bg-destructive/10 hover:text-destructive"
              onClick={() => signOut({ callbackUrl: "/" })}
            >
              <LogOut className="h-4 w-4 transition-transform duration-300 group-hover:rotate-12" />
              Sign Out
            </Button>
          </SidebarFooter>
        </Sidebar>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden" style={{ width: 'calc(100vw - 16rem)', minWidth: '768px' }}>
          <main className="flex-1 overflow-auto p-4 md:p-6 w-full">
            {children}
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
}
