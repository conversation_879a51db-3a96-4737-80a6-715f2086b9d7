import { getCategoryTree } from '@/actions/category.actions';
import { PageHeader } from '@/components/layout';
import { CategoryForm } from '../_components/category-form';

export const metadata = {
  title: 'Create Category - PhoneNeed',
  description: 'Add a new category to your catalog',
};

export default async function CreateCategoryPage() {
  // Get all categories for the parent selection
  const categories = await getCategoryTree();

  return (
    <div className="space-y-6">
      <PageHeader
        title="Create Category"
        description="Add a new category to your catalog"
        backHref="/dashboard/categories"
        backLabel="Back to Categories"
      />

      <CategoryForm categories={categories} />
    </div>
  );
}
