"use client";

import { useState } from "react";
import { ChevronRight, ChevronDown, Search, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

/**
 * A simplified category tree for selecting a parent category
 * Used in category creation/editing forms
 */
export function CategorySelectionTree({
  categories = [],
  selectedCategoryId = null,
  onChange,
  excludeCategoryId = null, // Category to exclude (e.g., when editing to prevent circular references)
}) {
  const [expandedNodes, setExpandedNodes] = useState(new Set());
  const [searchTerm, setSearchTerm] = useState("");
  
  // Toggle node expansion
  const toggleExpand = (id) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };
  
  // Expand all nodes
  const expandAll = () => {
    const allIds = getAllCategoryIds(categories);
    setExpandedNodes(new Set(allIds));
  };
  
  // Collapse all nodes
  const collapseAll = () => {
    setExpandedNodes(new Set());
  };
  
  // Get all category IDs for expand/collapse all
  const getAllCategoryIds = (cats) => {
    let ids = [];
    cats.forEach(cat => {
      ids.push(cat._id);
      if (cat.children && cat.children.length > 0) {
        ids = [...ids, ...getAllCategoryIds(cat.children)];
      }
    });
    return ids;
  };
  
  // Filter categories by search term
  const filterCategories = (cats, term) => {
    if (!term) return cats;
    
    return cats.filter(cat => {
      const matchesName = cat.name.toLowerCase().includes(term.toLowerCase());
      const hasMatchingChildren = cat.children && 
        filterCategories(cat.children, term).length > 0;
      
      return matchesName || hasMatchingChildren;
    }).map(cat => {
      if (!cat.children) return cat;
      
      return {
        ...cat,
        children: filterCategories(cat.children, term)
      };
    });
  };
  
  // Get filtered categories
  const filteredCategories = filterCategories(categories, searchTerm);
  
  // Clear selection
  const handleClearSelection = () => {
    if (onChange) {
      onChange(null);
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search categories..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-8"
        />
        {searchTerm && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1.5 h-7 w-7 p-0"
            onClick={() => setSearchTerm("")}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
      
      <div className="flex justify-between items-center">
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={expandAll}>
            Expand All
          </Button>
          <Button variant="outline" size="sm" onClick={collapseAll}>
            Collapse All
          </Button>
        </div>
        
        {selectedCategoryId && (
          <Button variant="ghost" size="sm" onClick={handleClearSelection}>
            Clear Selection
          </Button>
        )}
      </div>
      
      <div className="border rounded-md p-4 max-h-[300px] overflow-y-auto">
        <div className="space-y-0.5">
          <CategorySelectionItem
            category={{ _id: "none", name: "No Parent (Root Level)" }}
            level={0}
            isSelected={selectedCategoryId === null || selectedCategoryId === "none"}
            onSelect={() => onChange("none")}
          />
          
          {filteredCategories.map(category => (
            <CategorySelectionNode
              key={category._id}
              category={category}
              level={0}
              expanded={expandedNodes.has(category._id)}
              onToggleExpand={toggleExpand}
              selectedCategoryId={selectedCategoryId}
              onChange={onChange}
              excludeCategoryId={excludeCategoryId}
            />
          ))}
          
          {filteredCategories.length === 0 && (
            <div className="py-4 text-center text-muted-foreground">
              {searchTerm ? "No categories match your search" : "No categories found"}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Individual node in the selection tree
 */
function CategorySelectionNode({
  category,
  level = 0,
  expanded = false,
  onToggleExpand,
  selectedCategoryId,
  onChange,
  excludeCategoryId
}) {
  const hasChildren = category.children && category.children.length > 0;
  const isSelected = category._id === selectedCategoryId;
  const isExcluded = category._id === excludeCategoryId;
  
  // Calculate indentation
  const indentationStyle = {
    paddingLeft: `${level * 20}px`
  };
  
  // Handle selection
  const handleSelect = () => {
    if (isExcluded) return;
    if (onChange) {
      onChange(category._id);
    }
  };
  
  // Handle expand/collapse
  const handleToggleExpand = (e) => {
    e.stopPropagation();
    if (hasChildren) {
      onToggleExpand(category._id);
    }
  };
  
  return (
    <div>
      <CategorySelectionItem
        category={category}
        level={level}
        isSelected={isSelected}
        isExcluded={isExcluded}
        hasChildren={hasChildren}
        expanded={expanded}
        onToggleExpand={handleToggleExpand}
        onSelect={handleSelect}
      />
      
      {/* Render children if expanded */}
      {expanded && hasChildren && (
        <div>
          {category.children.map(child => (
            <CategorySelectionNode
              key={child._id}
              category={child}
              level={level + 1}
              expanded={expandedNodes ? expandedNodes.has(child._id) : false}
              onToggleExpand={onToggleExpand}
              selectedCategoryId={selectedCategoryId}
              onChange={onChange}
              excludeCategoryId={excludeCategoryId}
            />
          ))}
        </div>
      )}
    </div>
  );
}

/**
 * Individual item in the selection tree
 */
function CategorySelectionItem({
  category,
  level = 0,
  isSelected = false,
  isExcluded = false,
  hasChildren = false,
  expanded = false,
  onToggleExpand = () => {},
  onSelect = () => {}
}) {
  // Calculate indentation
  const indentationStyle = {
    paddingLeft: `${level * 20}px`
  };
  
  return (
    <div
      style={indentationStyle}
      className={cn(
        "flex items-center py-2 px-2 rounded-md transition-colors",
        isSelected ? "bg-primary/10 text-primary" : "hover:bg-muted/50",
        isExcluded ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
      )}
      onClick={isExcluded ? undefined : onSelect}
    >
      {/* Expand/collapse control */}
      <div className="mr-1 w-5 flex-shrink-0" onClick={onToggleExpand}>
        {hasChildren ? (
          expanded ? (
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          ) : (
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          )
        ) : (
          <div className="w-4" />
        )}
      </div>
      
      {/* Category name and status */}
      <div className="flex-1 flex items-center">
        <span className={cn(
          "font-medium",
          !category.is_active && "text-muted-foreground"
        )}>
          {category.name}
        </span>
        
        {/* Status badges */}
        {category.is_active === false && (
          <Badge variant="outline" className="ml-2 text-xs">Inactive</Badge>
        )}
        
        {isExcluded && (
          <Badge variant="outline" className="ml-2 text-xs">Current</Badge>
        )}
      </div>
    </div>
  );
}
