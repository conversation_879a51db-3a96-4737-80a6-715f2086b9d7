"use client";

import { useState, useEffect } from "react";
import { CategoryTree } from "./category-tree";
import { Skeleton } from "@/components/ui/skeleton";

/**
 * Client-side wrapper for the CategoryTree component
 * This prevents hydration mismatches with the dnd-kit library
 */
export function CategoryTreeWrapper({ initialCategories = [] }) {
  const [isClient, setIsClient] = useState(false);
  
  // Set isClient to true when component mounts on the client
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // Show a skeleton loader until the component is mounted on the client
  if (!isClient) {
    return (
      <div className="border rounded-md p-4 space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-10 w-28" />
        </div>
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className="h-10 w-full" />
          ))}
        </div>
      </div>
    );
  }
  
  // Render the actual tree component on the client
  return <CategoryTree categories={initialCategories} />;
}
