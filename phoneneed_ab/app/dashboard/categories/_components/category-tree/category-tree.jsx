"use client";

import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove
} from "@dnd-kit/sortable";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { CategoryNode } from "./category-node";
import { CategoryNodeDragOverlay } from "./category-node-drag-overlay";
import { moveCategory, bulkDeleteCategories } from "@/actions/category.actions";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";

/**
 * Main category tree component for management
 * Supports drag and drop, selection, and bulk operations
 */
export function CategoryTree({ categories = [] }) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  // Tree state
  const [items, setItems] = useState(categories);
  const [activeId, setActiveId] = useState(null);
  const [expandedNodes, setExpandedNodes] = useState(new Set());

  // Edit mode state
  const [mode, setMode] = useState("view"); // "view" or "edit"
  const [selectedCategories, setSelectedCategories] = useState(new Set());

  // Confirmation dialog state
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    title: "",
    description: "",
    onConfirm: () => {}
  });

  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );

  // Toggle node expansion
  const toggleExpand = (id) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // Toggle selection mode
  const toggleMode = () => {
    if (mode === "edit") {
      // Clear selections when exiting edit mode
      setSelectedCategories(new Set());
    }
    setMode(prev => prev === "view" ? "edit" : "view");
  };

  // Toggle category selection
  const toggleSelection = (id) => {
    if (mode !== "edit") return;

    setSelectedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // Handle drag start
  const handleDragStart = (event) => {
    if (mode === "edit") return; // Disable dragging in edit mode
    setActiveId(event.active.id);
  };

  // Handle drag end
  const handleDragEnd = (event) => {
    if (mode === "edit") return; // Disable dragging in edit mode

    setActiveId(null);
    const { active, over } = event;

    if (!over) return;

    if (active.id !== over.id) {
      // Optimistically update the UI
      setItems(prev => {
        const oldIndex = prev.findIndex(item => item._id === active.id);
        const newIndex = prev.findIndex(item => item._id === over.id);
        return arrayMove(prev, oldIndex, newIndex);
      });

      // Update in the database
      startTransition(async () => {
        try {
          const result = await moveCategory(active.id, over.id, "after");
          if (!result.success) {
            toast.error(result.message || "Failed to move category");
            // Refresh to get the correct state
            router.refresh();
          }
        } catch (error) {
          toast.error("An error occurred while moving the category");
          router.refresh();
        }
      });
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedCategories.size === 0) return;

    const categoryIds = Array.from(selectedCategories);
    const categoryCount = categoryIds.length;

    setConfirmDialog({
      isOpen: true,
      title: `Delete ${categoryCount} ${categoryCount === 1 ? 'category' : 'categories'}?`,
      description: "This will permanently delete the selected categories and all their subcategories. This action cannot be undone.",
      onConfirm: async () => {
        startTransition(async () => {
          try {
            const result = await bulkDeleteCategories(categoryIds);
            if (result.success) {
              toast.success(result.message || `${result.deletedCount} categories deleted`);
              setSelectedCategories(new Set());
              router.refresh();
            } else {
              toast.error(result.message || "Failed to delete categories");
            }
          } catch (error) {
            toast.error("An error occurred while deleting categories");
          }
        });
      }
    });
  };

  // Render the tree
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Button
          variant={mode === "edit" ? "default" : "outline"}
          onClick={toggleMode}
          disabled={isPending}
        >
          {mode === "view" ? "Select Mode" : "Exit Select Mode"}
        </Button>

        {mode === "edit" && selectedCategories.size > 0 && (
          <Button
            variant="destructive"
            onClick={handleBulkDelete}
            disabled={isPending}
            className="animate-in fade-in duration-200"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete {selectedCategories.size} {selectedCategories.size === 1 ? 'category' : 'categories'}
          </Button>
        )}
      </div>

      <div className="border rounded-md">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          modifiers={[restrictToVerticalAxis]}
        >
          <SortableContext items={items.map(item => item._id)} strategy={verticalListSortingStrategy}>
            <div className="p-4 space-y-0.5">
              {items.map(category => (
                <CategoryNode
                  key={category._id}
                  category={category}
                  level={0}
                  expanded={expandedNodes.has(category._id)}
                  expandedNodes={expandedNodes}
                  onToggleExpand={toggleExpand}
                  mode={mode}
                  selected={selectedCategories.has(category._id)}
                  onToggleSelect={toggleSelection}
                />
              ))}

              {items.length === 0 && (
                <div className="py-8 text-center text-muted-foreground">
                  No categories found. Create your first category to get started.
                </div>
              )}
            </div>
          </SortableContext>

          <DragOverlay>
            {activeId ? <CategoryNodeDragOverlay id={activeId} items={items} /> : null}
          </DragOverlay>
        </DndContext>
      </div>

      <AlertDialog open={confirmDialog.isOpen} onOpenChange={(open) => setConfirmDialog(prev => ({ ...prev, isOpen: open }))}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{confirmDialog.title}</AlertDialogTitle>
            <AlertDialogDescription>{confirmDialog.description}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDialog.onConfirm}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
