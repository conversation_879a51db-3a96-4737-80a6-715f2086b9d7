"use client";

import React, { useState, useTransition, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
  pointerWithin
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove
} from "@dnd-kit/sortable";
// Removed restrictToVerticalAxis to allow more flexible positioning
import { CategoryNode } from "./category-node";
import { CategoryNodeDragOverlay } from "./category-node-drag-overlay";
import { moveCategory, bulkDeleteCategories } from "@/actions/category.actions";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";

/**
 * Main category tree component for management
 * Supports drag and drop, selection, and bulk operations
 */
export function CategoryTree({ categories = [] }) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  // Tree state
  const [items, setItems] = useState(categories);
  const [activeId, setActiveId] = useState(null);
  const [expandedNodes, setExpandedNodes] = useState(new Set());
  const [dropIndicator, setDropIndicator] = useState({ id: null, zone: null });

  // Edit mode state
  const [mode, setMode] = useState("view"); // "view" or "edit"
  const [selectedCategories, setSelectedCategories] = useState(new Set());

  // Ref to store flattened items for drag overlay
  const flattenedItemsRef = useRef([]);

  // Confirmation dialog state
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    title: "",
    description: "",
    onConfirm: () => {}
  });

  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );

  // Helper function to flatten the tree structure
  const flattenTree = (items, result = []) => {
    if (!items || !Array.isArray(items)) return result;

    for (const item of items) {
      if (!item) continue;
      result.push(item);
      if (item.children && item.children.length > 0) {
        flattenTree(item.children, result);
      }
    }
    return result;
  };

  // Helper function to determine drop zone based on pointer position
  const determineDropZone = (activeRect, overRect) => {
    // Check if both rectangles are valid
    if (!activeRect || !overRect || !overRect.height) {
      return 'after'; // Default to 'after' if we can't determine
    }

    const threshold = overRect.height * 0.3;

    // Calculate relative position
    const relativeY = activeRect.top - overRect.top;

    if (relativeY < threshold) {
      return 'before';
    } else if (relativeY > overRect.height - threshold) {
      return 'after';
    } else {
      return 'inside';
    }
  };

  // Update flattened items when items change
  useEffect(() => {
    flattenedItemsRef.current = flattenTree(items);
  }, [items]);

  // Toggle node expansion
  const toggleExpand = (id) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // Toggle selection mode
  const toggleMode = () => {
    if (mode === "edit") {
      // Clear selections when exiting edit mode
      setSelectedCategories(new Set());
    }
    setMode(prev => prev === "view" ? "edit" : "view");
  };

  // Toggle category selection
  const toggleSelection = (id) => {
    if (mode !== "edit") return;

    setSelectedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // Custom collision detection that identifies drop zones
  const customCollisionDetection = (args) => {
    try {
      // Check if args is valid
      if (!args || !args.active || !args.active.rect || !args.active.rect.current) {
        return [];
      }

      // First get the elements that are being intersected with
      const pointerIntersections = pointerWithin(args);

      if (!pointerIntersections || !pointerIntersections.length) return [];

      // Add drop zone information to each intersection
      return pointerIntersections.map(intersection => {
        if (!intersection || !intersection.rect) {
          return intersection; // Return unchanged if invalid
        }

        const dropZone = determineDropZone(
          args.active.rect.current.translated,
          intersection.rect
        );

        return {
          ...intersection,
          data: {
            ...intersection.data,
            dropZone
          }
        };
      });
    } catch (error) {
      console.error('Error in collision detection:', error);
      return []; // Return empty array on error
    }
  };

  // Handle drag start
  const handleDragStart = (event) => {
    try {
      if (mode === "edit") return; // Disable dragging in edit mode
      if (!event || !event.active || !event.active.id) return;

      setActiveId(event.active.id);
      setDropIndicator({ id: null, zone: null });
    } catch (error) {
      console.error('Error in drag start handler:', error);
      setActiveId(null);
      setDropIndicator({ id: null, zone: null });
    }
  };

  // Handle drag over to show drop indicators
  const handleDragOver = (event) => {
    try {
      if (mode === "edit") return;

      if (!event) {
        setDropIndicator({ id: null, zone: null });
        return;
      }

      const { over, active } = event;
      if (!over || !over.id) {
        setDropIndicator({ id: null, zone: null });
        return;
      }

      // Set drop indicator
      const dropZone = over.data?.current?.dropZone || 'after';
      setDropIndicator({ id: over.id, zone: dropZone });

      // Auto-expand node when hovering with 'inside' drop zone
      if (dropZone === 'inside') {
        setExpandedNodes(prev => {
          const newSet = new Set(prev);
          newSet.add(over.id);
          return newSet;
        });
      }
    } catch (error) {
      console.error('Error in drag over handler:', error);
      setDropIndicator({ id: null, zone: null });
    }
  };

  // Helper function to find an item and its parent in the tree
  const findItemInTree = (items, id, parent = null, parentArray = null, index = -1) => {
    if (!items || !Array.isArray(items) || !id) return null;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (!item) continue;

      if (item._id === id) {
        return { item, parent, parentArray: items, index: i };
      }

      if (item.children && item.children.length > 0) {
        const result = findItemInTree(item.children, id, item, item.children, i);
        if (result) return result;
      }
    }
    return null;
  };

  // Helper function to update tree structure
  const updateTreeStructure = (items, sourceId, targetId, dropZone) => {
    if (!items || !Array.isArray(items) || !sourceId || !targetId || !dropZone) {
      return items;
    }

    try {
      // Create a deep copy of the items
      const newItems = JSON.parse(JSON.stringify(items));

      // Find the source item and its parent
      const sourceResult = findItemInTree(newItems, sourceId);
      if (!sourceResult) return newItems;

      const { item: sourceItem, parentArray: sourceParentArray, index: sourceIndex } = sourceResult;

      // Find target item
      const targetResult = findItemInTree(newItems, targetId);
      if (!targetResult) return newItems;

      const { item: targetItem, parent: targetParent, parentArray: targetParentArray, index: targetIndex } = targetResult;

      // Log the operation for debugging
      console.log(`Client: Moving ${sourceItem.name} (${sourceId}) ${dropZone} ${targetItem.name} (${targetId})`);
      console.log(`Client: Source index: ${sourceIndex}, Target index: ${targetIndex}`);

      // Check for circular reference (can't move a parent into its own child)
      if (dropZone === 'inside') {
        // Check if target is a descendant of source
        let current = targetParent;
        while (current) {
          if (current._id === sourceId) {
            // Circular reference detected, abort
            return newItems;
          }
          // Move up the tree
          const parentResult = findItemInTree(newItems, current._id);
          current = parentResult?.parent;
        }
      }

      // First, sort the parent array by position to ensure consistent order
      if (sourceParentArray !== targetParentArray) {
        // If moving between different parents, sort both arrays
        sourceParentArray.sort((a, b) => a.position - b.position);
        targetParentArray.sort((a, b) => a.position - b.position);
      } else {
        // If within same parent, sort once
        sourceParentArray.sort((a, b) => a.position - b.position);

        // Find the new indices after sorting
        const newSourceIndex = sourceParentArray.findIndex(item => item._id === sourceId);
        const newTargetIndex = sourceParentArray.findIndex(item => item._id === targetId);

        console.log(`Client: After sorting - Source index: ${newSourceIndex}, Target index: ${newTargetIndex}`);
      }

      // Remove source item from its current position
      const removedItem = sourceParentArray.find(item => item._id === sourceId);
      // Remove the source item from its parent array
      const currentSourceIndex = sourceParentArray.findIndex(item => item._id === sourceId);
      if (currentSourceIndex !== -1) {
        sourceParentArray.splice(currentSourceIndex, 1);
      }

      // Place the source item in its new position
      if (dropZone === 'inside') {
        // Add as a child of the target
        if (!targetItem.children) targetItem.children = [];

        // Sort children by position
        targetItem.children.sort((a, b) => a.position - b.position);

        // Add to the end
        targetItem.children.push(sourceItem);

        // Update positions for all children
        targetItem.children.forEach((child, index) => {
          child.position = index;
        });

        console.log(`Client: Added as child of ${targetItem.name}, new position: ${targetItem.children.length - 1}`);
      } else {
        // Find the target index in the sorted array
        const sortedTargetIndex = targetParentArray.findIndex(item => item._id === targetId);

        if (sortedTargetIndex === -1) {
          console.error('Client: Target not found in parent array after sorting');
          return newItems;
        }

        // Determine if we're moving up or down
        const sourcePosition = sourceItem.position;
        const targetPosition = targetItem.position;
        const isMovingUp = sourcePosition > targetPosition;

        // Calculate insert position based on direction and drop zone
        let insertIndex;

        if (dropZone === 'before') {
          // For 'before', always insert at the target index
          insertIndex = sortedTargetIndex;
        } else if (dropZone === 'after') {
          if (isMovingUp) {
            // When moving UP and dropZone is 'after', we need to insert at the target index
            // This is the key fix for the upward movement issue
            insertIndex = sortedTargetIndex;
          } else {
            // When moving DOWN and dropZone is 'after', we insert after the target
            insertIndex = sortedTargetIndex + 1;
          }
        }

        console.log(`Client: Moving ${isMovingUp ? 'UP' : 'DOWN'}, Source position: ${sourcePosition}, Target position: ${targetPosition}, Insert index: ${insertIndex}`);

        // Make sure insertIndex is valid
        const validInsertIndex = Math.max(0, Math.min(insertIndex, targetParentArray.length));

        // Insert at the calculated position
        targetParentArray.splice(validInsertIndex, 0, sourceItem);

        // Update positions for all siblings
        targetParentArray.forEach((sibling, index) => {
          sibling.position = index;
        });

        console.log(`Client: Added ${dropZone} ${targetItem.name} at index ${validInsertIndex}`);
        console.log('Client: New order:', targetParentArray.map(cat => `${cat.name} (${cat._id})`).join(', '));
      }

      return newItems;
    } catch (error) {
      console.error('Error updating tree structure:', error);
      return items; // Return original items on error
    }
  };

  // Handle drag end
  const handleDragEnd = (event) => {
    try {
      if (mode === "edit") return; // Disable dragging in edit mode

      // Always reset these states regardless of errors
      setActiveId(null);
      setDropIndicator({ id: null, zone: null });

      if (!event) return;

      const { active, over } = event;

      if (!active || !active.id || !over || !over.id) return;

      if (active.id !== over.id) {
        const dropZone = over.data?.current?.dropZone || 'after';

        // Optimistically update the UI
        setItems(prev => updateTreeStructure(prev, active.id, over.id, dropZone));

        // Log the operation for debugging
        console.log(`Moving category client-side: Source ID: ${active.id}, Target ID: ${over.id}, Drop Zone: ${dropZone}`);

        // Update in the database
        startTransition(async () => {
          try {
            const result = await moveCategory(active.id, over.id, dropZone);
            if (!result.success) {
              console.error('Move category failed:', result.message);
              toast.error(result.message || "Failed to move category");
              // Refresh to get the correct state
              router.refresh();
            } else {
              console.log('Move category succeeded:', result);
              toast.success("Category moved successfully");

              // Always refresh to ensure UI is in sync with server
              router.refresh();
            }
          } catch (error) {
            console.error('Error updating category position:', error);
            toast.error("An error occurred while moving the category");
            router.refresh();
          }
        });
      }
    } catch (error) {
      console.error('Error in drag end handler:', error);
      toast.error("An error occurred during drag operation");
      router.refresh(); // Refresh to ensure UI is in sync with server state
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedCategories.size === 0) return;

    const categoryIds = Array.from(selectedCategories);
    const categoryCount = categoryIds.length;

    setConfirmDialog({
      isOpen: true,
      title: `Delete ${categoryCount} ${categoryCount === 1 ? 'category' : 'categories'}?`,
      description: "This will permanently delete the selected categories and all their subcategories. This action cannot be undone.",
      onConfirm: async () => {
        startTransition(async () => {
          try {
            const result = await bulkDeleteCategories(categoryIds);
            if (result.success) {
              toast.success(result.message || `${result.deletedCount} categories deleted`);
              setSelectedCategories(new Set());
              router.refresh();
            } else {
              toast.error(result.message || "Failed to delete categories");
            }
          } catch (error) {
            toast.error("An error occurred while deleting categories");
          }
        });
      }
    });
  };

  // Render the tree
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Button
            variant={mode === "edit" ? "default" : "outline"}
            onClick={toggleMode}
            disabled={isPending}
          >
            {mode === "view" ? "Select Mode" : "Exit Select Mode"}
          </Button>

          {mode === "edit" && selectedCategories.size > 0 && (
            <Button
              variant="destructive"
              onClick={handleBulkDelete}
              disabled={isPending}
              className="animate-in fade-in duration-200"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete {selectedCategories.size} {selectedCategories.size === 1 ? 'category' : 'categories'}
            </Button>
          )}
        </div>

        {mode === "view" && (
          <div className="text-sm text-muted-foreground bg-muted/50 p-2 rounded-md">
            <p>Drag categories to reorder them. Drag to the middle of a category to make it a child, or to the top/bottom edges to place before/after.</p>
          </div>
        )}
      </div>

      <div className="border rounded-md">
        <DndContext
          sensors={sensors}
          collisionDetection={customCollisionDetection}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
          // Removed restrictToVerticalAxis to allow more flexible positioning
        >
          <SortableContext items={flattenTree(items).map(item => item._id)} strategy={verticalListSortingStrategy}>
            <div className="p-4 space-y-0.5 category-tree-container max-h-[600px] overflow-y-auto">
              {items.map(category => (
                <CategoryNode
                  key={category._id}
                  category={category}
                  level={0}
                  expanded={expandedNodes.has(category._id)}
                  expandedNodes={expandedNodes}
                  onToggleExpand={toggleExpand}
                  mode={mode}
                  selected={selectedCategories.has(category._id)}
                  selectedCategories={selectedCategories}
                  onToggleSelect={toggleSelection}
                  dropIndicator={dropIndicator.id === category._id ? dropIndicator.zone : null}
                />
              ))}

              {items.length === 0 && (
                <div className="py-8 text-center text-muted-foreground">
                  No categories found. Create your first category to get started.
                </div>
              )}
            </div>
          </SortableContext>

          <DragOverlay>
            {activeId ? <CategoryNodeDragOverlay id={activeId} items={flattenedItemsRef.current} /> : null}
          </DragOverlay>
        </DndContext>
      </div>

      <AlertDialog open={confirmDialog.isOpen} onOpenChange={(open) => setConfirmDialog(prev => ({ ...prev, isOpen: open }))}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{confirmDialog.title}</AlertDialogTitle>
            <AlertDialogDescription>{confirmDialog.description}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDialog.onConfirm}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
