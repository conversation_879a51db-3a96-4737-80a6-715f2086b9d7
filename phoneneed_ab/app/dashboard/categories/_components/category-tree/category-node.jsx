"use client";

import { useState } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { ChevronRight, ChevronDown, GripVertical, Pencil, Eye } from "lucide-react";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";

/**
 * Individual category node in the tree
 */
export function CategoryNode({
  category,
  level = 0,
  expanded = false,
  expandedNodes = new Set(),
  onToggleExpand,
  mode = "view", // "view" or "edit"
  selected = false,
  selectedCategories = new Set(),
  onToggleSelect,
  dropIndicator = null // 'before', 'after', or 'inside'
}) {
  const hasChildren = category.children && category.children.length > 0;

  // Set up sortable functionality
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({
    id: category._id,
    disabled: mode === "edit" // Disable sorting in edit mode
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  // Handle expand/collapse
  const handleToggleExpand = (e) => {
    e.stopPropagation();
    if (hasChildren) {
      onToggleExpand(category._id);
    }
  };

  // Handle selection
  const handleSelect = (e) => {
    e.stopPropagation();
    onToggleSelect(category._id);
  };

  // Calculate indentation
  const indentationStyle = {
    paddingLeft: `${level * 20}px`
  };

  return (
    <div className="relative">
      {/* Before drop indicator */}
      {dropIndicator === 'before' && (
        <div className="drop-indicator-before" />
      )}

      <div
        ref={setNodeRef}
        style={{ ...style, ...indentationStyle }}
        className={cn(
          "flex items-center py-2 px-2 rounded-md transition-colors relative",
          isDragging ? "opacity-50" : "",
          mode === "view" ? "hover:bg-muted/50 group" : "",
          selected ? "bg-muted" : "",
          dropIndicator === 'inside' ? "drop-indicator-inside" : ""
        )}
        onClick={mode === "edit" ? handleSelect : undefined}
      >
        {/* Expand/collapse control */}
        <div className="mr-1 w-5 flex-shrink-0" onClick={handleToggleExpand}>
          {hasChildren ? (
            expanded ? (
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-4 w-4 text-muted-foreground" />
            )
          ) : (
            <div className="w-4" />
          )}
        </div>

        {/* Selection checkbox (edit mode) */}
        {mode === "edit" && (
          <Checkbox
            checked={selected}
            onCheckedChange={() => onToggleSelect(category._id)}
            className="mr-2"
            onClick={(e) => e.stopPropagation()}
          />
        )}

        {/* Drag handle (view mode) */}
        {mode === "view" && (
          <div
            {...attributes}
            {...listeners}
            className="mr-2 cursor-grab opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <GripVertical className="h-4 w-4 text-muted-foreground" />
          </div>
        )}

        {/* Category name and status */}
        <div className="flex-1 flex items-center">
          <span className={cn(
            "font-medium",
            !category.is_active && "text-muted-foreground line-through"
          )}>
            {category.name}
          </span>

          {/* Status badges */}
          <div className="ml-2 flex gap-1">
            {!category.is_active && (
              <Badge variant="outline" className="text-xs">Inactive</Badge>
            )}
            {category.is_primary && (
              <Badge variant="secondary" className="text-xs">Primary</Badge>
            )}
            {category.is_menu_item && (
              <Badge variant="secondary" className="text-xs">Menu</Badge>
            )}
          </div>
        </div>

        {/* Actions */}
        {mode === "view" && (
          <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="ghost" size="icon" asChild>
              <Link href={`/dashboard/categories/${category._id}`}>
                <Eye className="h-4 w-4" />
              </Link>
            </Button>
            <Button variant="ghost" size="icon" asChild>
              <Link href={`/dashboard/categories/${category._id}/edit`}>
                <Pencil className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        )}
      </div>

      {/* After drop indicator */}
      {dropIndicator === 'after' && (
        <div className="drop-indicator-after" />
      )}

      {/* Render children if expanded */}
      {expanded && hasChildren && (
        <div>
          {category.children.map(child => (
            <CategoryNode
              key={child._id}
              category={child}
              level={level + 1}
              expanded={expandedNodes ? expandedNodes.has(child._id) : false}
              expandedNodes={expandedNodes}
              onToggleExpand={onToggleExpand}
              mode={mode}
              selected={selectedCategories.has(child._id)}
              selectedCategories={selectedCategories}
              onToggleSelect={onToggleSelect}
              dropIndicator={null} // Only pass dropIndicator to direct children from parent
            />
          ))}
        </div>
      )}
    </div>
  );
}
