"use client";

/**
 * Drag overlay component for the category tree
 * Shows a simplified version of the category node while dragging
 */
export function CategoryNodeDragOverlay({ id, items }) {
  // Find the category by id
  const category = items.find(item => item._id === id);

  if (!category) return null;

  return (
    <div className="flex items-center py-2 px-2 rounded-md bg-background border shadow-md">
      <div className="mr-2 w-5"></div>
      <span className="font-medium">{category.name}</span>
      <div className="ml-2 text-xs text-muted-foreground">
        Drag to reposition or nest
      </div>
    </div>
  );
}
