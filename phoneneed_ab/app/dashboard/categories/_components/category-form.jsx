"use client";

import { useEffect, useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { createCategorySchema, updateCategorySchema } from "@/schemas";
import { createCategory, updateCategory } from "@/actions/category.actions";
import { ImageUpload } from "@/components/forms";
import { generateSlug } from "@/lib/utils";
import { CategorySelectionTree } from "./category-selection-tree";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";

/**
 * Form component for creating or editing a category
 */
export function CategoryForm({ category, categories = [] }) {
  const isEditMode = !!category;
  const router = useRouter();
  
  // For transitions
  const [isPending, startTransition] = useTransition();
  
  // Form state
  const [formState, setFormState] = useState({ 
    isSubmitting: false 
  });
  
  // Set up form with default values
  const form = useForm({
    resolver: zodResolver(isEditMode ? updateCategorySchema : createCategorySchema),
    defaultValues: {
      name: category?.name || "",
      slug: category?.slug || "",
      parent_id: category?.parent_id || "none",
      is_active: category?.is_active ?? true,
      is_primary: category?.is_primary ?? false,
      is_menu_item: category?.is_menu_item ?? false,
      img: category?.img || "",
      title: category?.title || "",
      description: category?.description || "",
      seo_title: category?.seo_title || "",
      seo_description: category?.seo_description || "",
    }
  });
  
  // Auto-generate slug when name changes
  const watchName = form.watch("name");
  useEffect(() => {
    if (watchName && !form.getValues("slug")) {
      const slug = generateSlug(watchName);
      form.setValue("slug", slug, { shouldValidate: true });
    }
  }, [watchName, form]);
  
  // Handle form submission
  const onSubmit = (values) => {
    setFormState({ isSubmitting: true });
    
    startTransition(async () => {
      try {
        // Submit the form data to the server
        let result;
        if (isEditMode) {
          result = await updateCategory(category._id, values);
        } else {
          result = await createCategory(values);
        }
        
        // Handle the result
        if (result.success) {
          toast.success(result.message || `Category ${isEditMode ? 'updated' : 'created'} successfully`);
          
          // Redirect to the appropriate page
          if (result.redirect) {
            router.push(result.redirectUrl);
          } else {
            router.push('/dashboard/categories');
          }
          router.refresh();
        } else {
          toast.error(result.message || `Failed to ${isEditMode ? 'update' : 'create'} category`);
          setFormState({ isSubmitting: false });
        }
      } catch (error) {
        console.error(`Error ${isEditMode ? 'updating' : 'creating'} category:`, error);
        toast.error(`An error occurred while ${isEditMode ? 'updating' : 'creating'} the category`);
        setFormState({ isSubmitting: false });
      }
    });
  };
  
  return (
    <div className="grid md:grid-cols-3 gap-6">
      {/* Left column - Category selection tree */}
      <div className="md:col-span-1">
        <Card>
          <CardHeader>
            <CardTitle>Parent Category</CardTitle>
            <CardDescription>
              Select a parent category or leave as "No Parent" for a top-level category
            </CardDescription>
          </CardHeader>
          <CardContent>
            <CategorySelectionTree
              categories={categories}
              selectedCategoryId={form.getValues("parent_id")}
              onChange={(value) => form.setValue("parent_id", value)}
              excludeCategoryId={isEditMode ? category._id : null}
            />
          </CardContent>
        </Card>
      </div>
      
      {/* Right column - Category form */}
      <div className="md:col-span-2">
        <Card>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <CardHeader>
                <CardTitle>{isEditMode ? 'Edit Category' : 'Create New Category'}</CardTitle>
                <CardDescription>
                  {isEditMode ? 'Update category information' : 'Add a new category to your catalog'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Basic Information</h3>
                  
                  {/* Name field */}
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Accessories" {...field} />
                        </FormControl>
                        <FormDescription>
                          The name of the category as it will appear to users.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {/* Slug field */}
                  <FormField
                    control={form.control}
                    name="slug"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Slug</FormLabel>
                        <FormControl>
                          <Input placeholder="accessories" {...field} />
                        </FormControl>
                        <FormDescription>
                          URL-friendly version of the name. Auto-generated from the name if left empty.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {/* Status switches */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="is_active"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Active</FormLabel>
                            <FormDescription>
                              Show in catalog
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="is_primary"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Primary</FormLabel>
                            <FormDescription>
                              Featured category
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="is_menu_item"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Menu Item</FormLabel>
                            <FormDescription>
                              Show in navigation
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  {/* Image field */}
                  <FormField
                    control={form.control}
                    name="img"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Image</FormLabel>
                        <FormControl>
                          <ImageUpload
                            value={field.value}
                            onChange={field.onChange}
                          />
                        </FormControl>
                        <FormDescription>
                          Category image (optional). Enter a URL or upload an image.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                {/* SEO Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">SEO Information</h3>
                  
                  {/* Title field */}
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Category Title" {...field} />
                        </FormControl>
                        <FormDescription>
                          Page title for SEO (optional). Defaults to category name if left empty.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {/* Description field */}
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Category description..."
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Category description (optional).
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {/* SEO Title field */}
                  <FormField
                    control={form.control}
                    name="seo_title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SEO Title</FormLabel>
                        <FormControl>
                          <Input placeholder="SEO Title" {...field} />
                        </FormControl>
                        <FormDescription>
                          Custom SEO title (optional). Defaults to title if left empty.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {/* SEO Description field */}
                  <FormField
                    control={form.control}
                    name="seo_description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SEO Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="SEO description..."
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Meta description for SEO (optional).
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/dashboard/categories')}
                  disabled={isPending || formState.isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isPending || formState.isSubmitting}>
                  {(isPending || formState.isSubmitting)
                    ? (isEditMode ? 'Updating...' : 'Creating...')
                    : (isEditMode ? 'Update Category' : 'Create Category')
                  }
                </Button>
              </CardFooter>
            </form>
          </Form>
        </Card>
      </div>
    </div>
  );
}
