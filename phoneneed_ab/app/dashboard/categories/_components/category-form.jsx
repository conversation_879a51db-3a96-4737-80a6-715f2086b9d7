"use client";

import { useEffect, useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { createCategorySchema, updateCategorySchema } from "@/schemas";
import { createCategory, updateCategory } from "@/actions/category.actions";
import { ImageUpload, FormGroup } from "@/components/forms";
import { generateSlug } from "@/lib/utils";
import { CategorySelectionTree } from "./category-selection-tree/category-selection-tree";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { X, Plus } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

/**
 * Form component for creating or editing a category
 */
export function CategoryForm({ category, categories = [] }) {
  const isEditMode = !!category;
  const router = useRouter();

  // For transitions
  const [isPending, startTransition] = useTransition();

  // Form state
  const [formState, setFormState] = useState({
    isSubmitting: false
  });

  // Set up form with default values
  const form = useForm({
    resolver: zodResolver(isEditMode ? updateCategorySchema : createCategorySchema),
    defaultValues: {
      name: category?.name || "",
      slug: category?.slug || "",
      parent_id: category?.parent_id || "none",
      is_active: category?.is_active ?? true,
      is_primary: category?.is_primary ?? false,
      is_menu_item: category?.is_menu_item ?? false,
      is_shopify_collection: category?.is_shopify_collection ?? false,
      shopify_data: category?.shopify_data ?? {
        is_synced: false,
        shopify_handle: '',
        shopify_id: '',
        rules_match: 'all',
        rules: [],
      },
      img: category?.img || "",
      title: category?.title || "",
      description: category?.description || "",
      seo_title: category?.seo_title || "",
      seo_description: category?.seo_description || "",
    }
  });

  // Initialize selectedCategoryId state
  const [selectedCategoryId, setSelectedCategoryId] = useState(category?.parent_id || "none");

  // Track if slug has been manually edited
  const [slugManuallyEdited, setSlugManuallyEdited] = useState(false);

  // Track if Shopify handle has been manually edited
  const [shopifyHandleManuallyEdited, setShopifyHandleManuallyEdited] = useState(false);

  // Track if title has been manually edited
  const [titleManuallyEdited, setTitleManuallyEdited] = useState(false);

  // Track if SEO title has been manually edited
  const [seoTitleManuallyEdited, setSeoTitleManuallyEdited] = useState(false);

  // Auto-generate slug when name changes
  const watchName = form.watch("name");
  const watchIsShopifyCollection = form.watch("is_shopify_collection");
  const watchShopifyHandle = form.watch("shopify_data.shopify_handle");

  // Add slug to watched values
  const watchSlug = form.watch("slug");

  useEffect(() => {
    if (watchName && !slugManuallyEdited) {
      const slug = generateSlug(watchName);
      form.setValue("slug", slug, { shouldValidate: true });
    }
  }, [watchName, form, slugManuallyEdited]);

  // Auto-generate Shopify handle from slug
  useEffect(() => {
    if (watchSlug && !shopifyHandleManuallyEdited) {
      form.setValue("shopify_data.shopify_handle", watchSlug, { shouldValidate: true });
    }
  }, [watchSlug, form, shopifyHandleManuallyEdited]);

  // Auto-generate title from name
  useEffect(() => {
    if (watchName && !titleManuallyEdited) {
      form.setValue("title", watchName, { shouldValidate: true });
    }
  }, [watchName, form, titleManuallyEdited]);

  // Auto-generate SEO title from name
  useEffect(() => {
    if (watchName && !seoTitleManuallyEdited) {
      form.setValue("seo_title", watchName, { shouldValidate: true });
    }
  }, [watchName, form, seoTitleManuallyEdited]);

  // Handle form submission
  const onSubmit = (values) => {
    setFormState({ isSubmitting: true });

    startTransition(async () => {
      try {
        // Submit the form data to the server
        let result;
        if (isEditMode) {
          result = await updateCategory(category._id, values);
        } else {
          result = await createCategory(values);
        }

        // Handle the result
        if (result.success) {
          toast.success(result.message || `Category ${isEditMode ? 'updated' : 'created'} successfully`);

          // Redirect to the appropriate page
          if (result.redirect) {
            router.push(result.redirectUrl);
          } else {
            router.push('/dashboard/categories');
          }
          router.refresh();
        } else {
          toast.error(result.message || `Failed to ${isEditMode ? 'update' : 'create'} category`);
          setFormState({ isSubmitting: false });
        }
      } catch (error) {
        console.error(`Error ${isEditMode ? 'updating' : 'creating'} category:`, error);
        toast.error(`An error occurred while ${isEditMode ? 'updating' : 'creating'} the category`);
        setFormState({ isSubmitting: false });
      }
    });
  };

  return (
    <div className="grid md:grid-cols-3 gap-6">
      {/* Left column - Category selection tree */}
      <div className="md:col-span-1">
        <Card>
          <CardHeader>
            <CardTitle>Parent Category</CardTitle>
            <CardDescription>
              Select a parent category or leave as "No Parent" for a top-level category
            </CardDescription>
          </CardHeader>
          <CardContent>
            <CategorySelectionTree
              categories={categories}
              selectedCategoryId={selectedCategoryId}
              onChange={(value) => {
                setSelectedCategoryId(value);
                form.setValue("parent_id", value, { shouldValidate: true });
              }}
              excludeCategoryId={isEditMode ? category._id : null}
            />
          </CardContent>
        </Card>
      </div>

      {/* Right column - Category form */}
      <div className="md:col-span-2">
        <Card>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <CardHeader>
                <CardTitle>{isEditMode ? 'Edit Category' : 'Create New Category'}</CardTitle>
                <CardDescription>
                  {isEditMode ? 'Update category information' : 'Add a new category to your catalog'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Basic Information */}
                <FormGroup title="Basic Information">

                  {/* Name field */}
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Accessories" {...field} />
                        </FormControl>
                        <FormDescription>
                          The name of the category as it will appear to users.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Slug field */}
                  <FormField
                    control={form.control}
                    name="slug"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Slug</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="accessories"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                              // If user manually edits the slug, set the flag
                              if (e.target.value !== generateSlug(watchName)) {
                                setSlugManuallyEdited(true);
                              }
                              // If user clears the slug, allow auto-generation again
                              if (e.target.value === '') {
                                setSlugManuallyEdited(false);
                              }
                            }}
                          />
                        </FormControl>
                        <FormDescription>
                          URL-friendly version of the name. Auto-generated from the name if left empty.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Status switches */}
                  <FormGroup title="Status Options" divider>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="is_active"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Active</FormLabel>
                            <FormDescription>
                              Show in catalog
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="is_primary"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Primary</FormLabel>
                            <FormDescription>
                              Featured category
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="is_menu_item"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Menu Item</FormLabel>
                            <FormDescription>
                              Show in navigation
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="is_shopify_collection"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Shopify Collection</FormLabel>
                            <FormDescription>
                              Is a Shopify product collection
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  </FormGroup>

                  {/* Image field */}
                  <FormField
                    control={form.control}
                    name="img"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Image</FormLabel>
                        <FormControl>
                          <ImageUpload
                            value={field.value}
                            onChange={field.onChange}
                          />
                        </FormControl>
                        <FormDescription>
                          Category image (optional). Enter a URL or upload an image.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>

                {/* SEO Information */}
                <FormGroup title="SEO Information" divider>

                  {/* Title field */}
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Category Title"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                              // If user manually edits the title, set the flag
                              if (e.target.value !== watchName) {
                                setTitleManuallyEdited(true);
                              }
                              // If user clears the title, allow auto-generation again
                              if (e.target.value === '') {
                                setTitleManuallyEdited(false);
                              }
                            }}
                          />
                        </FormControl>
                        <FormDescription>
                          Page title for SEO (auto-generated from name if empty)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Description field */}
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Category description..."
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Category description (optional).
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* SEO Title field */}
                  <FormField
                    control={form.control}
                    name="seo_title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SEO Title</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="SEO Title"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                              // If user manually edits the SEO title, set the flag
                              if (e.target.value !== watchName) {
                                setSeoTitleManuallyEdited(true);
                              }
                              // If user clears the SEO title, allow auto-generation again
                              if (e.target.value === '') {
                                setSeoTitleManuallyEdited(false);
                              }
                            }}
                          />
                        </FormControl>
                        <FormDescription>
                          Custom SEO title (auto-generated from name if empty)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* SEO Description field */}
                  <FormField
                    control={form.control}
                    name="seo_description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SEO Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="SEO description..."
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Meta description for SEO (optional).
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>

                {/* Shopify Integration */}
                <FormGroup title="Shopify Integration" divider>

                  <FormField
                    control={form.control}
                    name="shopify_data.is_synced"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>Synced</FormLabel>
                          <FormDescription>
                            Category is synced with Shopify
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="shopify_data.shopify_handle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Shopify Handle</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="shopify-collection-handle"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                              // If user manually edits the handle, set the flag
                              if (e.target.value !== generateSlug(watchName)) {
                                setShopifyHandleManuallyEdited(true);
                              }
                              // If user clears the handle, allow auto-generation again
                              if (e.target.value === '') {
                                setShopifyHandleManuallyEdited(false);
                              }
                            }}
                          />
                        </FormControl>
                        <FormDescription>
                          Shopify collection handle (auto-generated from name if empty)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="shopify_data.shopify_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Shopify ID</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="gid://shopify/Collection/123456789"
                            {...field}
                            disabled={!form.watch("shopify_data.is_synced")}
                            readOnly={form.watch("shopify_data.is_synced")}
                          />
                        </FormControl>
                        <FormDescription>
                          Shopify collection ID (filled automatically when synced)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Rules field - only shown when is_shopify_collection is checked */}
                  {form.watch("is_shopify_collection") && (
                    <FormGroup title="Collection Rules">

                      {/* Rules match type */}
                      <FormField
                        control={form.control}
                        name="shopify_data.rules_match"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Products must match:</FormLabel>
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center space-x-2">
                                <FormControl>
                                  <input
                                    type="radio"
                                    checked={field.value === 'all'}
                                    onChange={() => field.onChange('all')}
                                    id="match-all"
                                  />
                                </FormControl>
                                <label htmlFor="match-all">all conditions</label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <FormControl>
                                  <input
                                    type="radio"
                                    checked={field.value === 'any'}
                                    onChange={() => field.onChange('any')}
                                    id="match-any"
                                  />
                                </FormControl>
                                <label htmlFor="match-any">any condition</label>
                              </div>
                            </div>
                          </FormItem>
                        )}
                      />

                      {/* Rules list */}
                      <div className="space-y-4">
                        {form.watch("shopify_data.rules").map((rule, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <Select
                              value={rule.column}
                              onValueChange={(value) => {
                                const newRules = [...form.getValues("shopify_data.rules")];
                                newRules[index] = { ...newRules[index], column: value };
                                form.setValue("shopify_data.rules", newRules);
                              }}
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Select property" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="title">Product title</SelectItem>
                                <SelectItem value="type">Product type</SelectItem>
                                <SelectItem value="vendor">Product vendor</SelectItem>
                                <SelectItem value="variant_price">Product price</SelectItem>
                                <SelectItem value="tag">Product tag</SelectItem>
                              </SelectContent>
                            </Select>

                            <Select
                              value={rule.relation}
                              onValueChange={(value) => {
                                const newRules = [...form.getValues("shopify_data.rules")];
                                newRules[index] = { ...newRules[index], relation: value };
                                form.setValue("shopify_data.rules", newRules);
                              }}
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Select condition" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="equals">is equal to</SelectItem>
                                <SelectItem value="not_equals">is not equal to</SelectItem>
                                <SelectItem value="contains">contains</SelectItem>
                                <SelectItem value="not_contains">does not contain</SelectItem>
                                <SelectItem value="starts_with">starts with</SelectItem>
                                <SelectItem value="ends_with">ends with</SelectItem>
                                <SelectItem value="greater_than">is greater than</SelectItem>
                                <SelectItem value="less_than">is less than</SelectItem>
                              </SelectContent>
                            </Select>

                            <Input
                              value={rule.condition}
                              onChange={(e) => {
                                const newRules = [...form.getValues("shopify_data.rules")];
                                newRules[index] = { ...newRules[index], condition: e.target.value };
                                form.setValue("shopify_data.rules", newRules);
                              }}
                              className="flex-1"
                              placeholder="Enter value"
                            />

                            <Button
                              type="button"
                              variant="outline"
                              size="icon"
                              onClick={() => {
                                const newRules = form.getValues("shopify_data.rules").filter((_, i) => i !== index);
                                form.setValue("shopify_data.rules", newRules);
                              }}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}

                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const currentRules = form.getValues("shopify_data.rules");
                            form.setValue("shopify_data.rules", [
                              ...currentRules,
                              { column: "title", relation: "contains", condition: "" }
                            ]);
                          }}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add condition
                        </Button>
                      </div>

                      <FormDescription>
                        Define rules for automatic product assignment to this collection
                      </FormDescription>
                    </FormGroup>
                  )}
                </FormGroup>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/dashboard/categories')}
                  disabled={isPending || formState.isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isPending || formState.isSubmitting}>
                  {(isPending || formState.isSubmitting)
                    ? (isEditMode ? 'Updating...' : 'Creating...')
                    : (isEditMode ? 'Update Category' : 'Create Category')
                  }
                </Button>
              </CardFooter>
            </form>
          </Form>
        </Card>
      </div>
    </div>
  );
}
