import { getCategoryTree } from '@/actions/category.actions';
import { PageHeader } from '@/components/layout';
import { CategoryTreeWrapper } from './_components/category-tree-wrapper';
import { FolderTree } from 'lucide-react';

export const metadata = {
  title: 'Categories - PhoneNeed',
  description: 'Manage product categories in your catalog',
};

export default async function CategoriesPage() {
  // Get the category tree
  const categories = await getCategoryTree();

  return (
    <div className="space-y-6">
      <PageHeader
        title="Categories"
        description="Manage product categories in your catalog"
        actionHref="/dashboard/categories/new"
        actionLabel="Add Category"
      />

      <CategoryTreeWrapper initialCategories={categories} />
    </div>
  );
}
