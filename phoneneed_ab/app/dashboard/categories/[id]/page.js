import { getCategoryById } from '@/actions/category.actions';
import { PageHeader } from '@/components/layout';
import { EntityDetails } from '@/components/data-display';
import { formatDate } from '@/lib/utils';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export const metadata = {
  title: 'Category Details - PhoneNeed',
  description: 'View category details',
};

export default async function CategoryDetailPage({ params }) {
  const { id } = params;

  try {
    // Check if the ID is a valid MongoDB ObjectId format
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      console.error(`Invalid category ID format: ${id}`);
      notFound();
    }

    // Get the category
    const category = await getCategoryById(id);

    // Format the category data for display
    const formattedCategory = {
      ...category,
      created_at: formatDate(category.created_at),
      updated_at: formatDate(category.updated_at),
      is_active: category.is_active ? 'Yes' : 'No',
      is_primary: category.is_primary ? 'Yes' : 'No',
      is_menu_item: category.is_menu_item ? 'Yes' : 'No',
      level: category.level.toString(),
    };

    // Define the fields to display
    const fields = [
      { name: 'name', label: 'Name' },
      { name: 'slug', label: 'Slug' },
      { name: 'parent_id', label: 'Parent ID' },
      { name: 'level', label: 'Level' },
      { name: 'path', label: 'Path' },
      { name: 'position', label: 'Position' },
      { name: 'is_active', label: 'Active' },
      { name: 'is_primary', label: 'Primary' },
      { name: 'is_menu_item', label: 'Menu Item' },
      { name: 'title', label: 'Title' },
      { name: 'description', label: 'Description' },
      { name: 'seo_title', label: 'SEO Title' },
      { name: 'seo_description', label: 'SEO Description' },
      { name: 'created_at', label: 'Created At' },
      { name: 'updated_at', label: 'Updated At' },
    ];

    return (
      <div className="space-y-6">
        <PageHeader
          title="Category Details"
          description="View and manage category information"
          backHref="/dashboard/categories"
          backLabel="Back to Categories"
        >
          <Button asChild>
            <Link href={`/dashboard/categories/${id}/edit`}>
              Edit Category
            </Link>
          </Button>
        </PageHeader>

        <div className="flex flex-wrap gap-2 mb-6">
          {!category.is_active && (
            <Badge variant="outline">Inactive</Badge>
          )}
          {category.is_primary && (
            <Badge variant="secondary">Primary</Badge>
          )}
          {category.is_menu_item && (
            <Badge variant="secondary">Menu Item</Badge>
          )}
        </div>

        <EntityDetails
          title={category.name}
          description="Category details and information"
          entity={formattedCategory}
          fields={fields}
          editHref={`/dashboard/categories/${id}/edit`}
          imageField="img"
        />
      </div>
    );
  } catch (error) {
    console.error(`Error loading category ${id}:`, error);
    notFound();
  }
}
