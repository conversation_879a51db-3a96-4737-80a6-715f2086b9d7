import { getCategoryById, getCategoryTree } from '@/actions/category.actions';
import { notFound } from 'next/navigation';
import { PageHeader } from '@/components/layout';
import { CategoryForm } from '../../_components/category-form';

export const metadata = {
  title: 'Edit Category - PhoneNeed',
  description: 'Edit category details',
};

export default async function EditCategoryPage({ params }) {
  const { id } = params;

  try {
    // Check if the ID is a valid MongoDB ObjectId format
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      console.error(`Invalid category ID format: ${id}`);
      notFound();
    }

    // Get the category and all categories for the parent selection
    const [category, categories] = await Promise.all([
      getCategoryById(id),
      getCategoryTree()
    ]);

    return (
      <div className="space-y-6">
        <PageHeader
          title="Edit Category"
          description="Update category information"
          backHref={`/dashboard/categories/${id}`}
          backLabel="Back to Category"
        />

        <CategoryForm category={category} categories={categories} />
      </div>
    );
  } catch (error) {
    console.error(`Error loading category ${id} for editing:`, error);
    notFound();
  }
}
