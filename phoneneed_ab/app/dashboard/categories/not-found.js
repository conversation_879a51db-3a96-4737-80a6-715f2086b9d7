import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';

export default function CategoryNotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] text-center px-4">
      <div className="rounded-full bg-muted p-6 mb-6">
        <AlertCircle className="h-12 w-12 text-muted-foreground" />
      </div>
      <h1 className="text-3xl font-bold mb-2">Category Not Found</h1>
      <p className="text-muted-foreground mb-6 max-w-md">
        The category you are looking for doesn't exist or has been removed.
      </p>
      <div className="flex gap-4">
        <Button asChild>
          <Link href="/dashboard/categories">
            View All Categories
          </Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href="/dashboard/categories/new">
            Create New Category
          </Link>
        </Button>
      </div>
    </div>
  );
}
