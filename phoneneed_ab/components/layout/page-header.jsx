"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ChevronLeft, Plus } from "lucide-react";

/**
 * Consistent page header component for all dashboard pages
 */
export function PageHeader({
  title,
  description,
  backHref,
  backLabel = "Back",
  actionHref,
  actionLabel = "Create",
  children,
}) {
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between mb-6">
      <div className="space-y-1">
        {backHref && (
          <Button
            variant="link"
            className="px-0 text-muted-foreground"
            asChild
          >
            <Link href={backHref}>
              <ChevronLeft className="mr-1 h-4 w-4" />
              {backLabel}
            </Link>
          </Button>
        )}
        <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
        {description && (
          <p className="text-muted-foreground">{description}</p>
        )}
      </div>
      <div className="flex items-center gap-2">
        {actionHref && (
          <Button asChild>
            <Link href={actionHref}>
              <Plus className="mr-2 h-4 w-4" />
              {actionLabel}
            </Link>
          </Button>
        )}
        {children}
      </div>
    </div>
  );
}
