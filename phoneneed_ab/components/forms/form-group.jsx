"use client";

import { cn } from "@/lib/utils";

/**
 * FormGroup component for grouping related form fields
 *
 * @param {Object} props
 * @param {React.ReactNode} props.children - The form fields to be grouped
 * @param {string} props.title - The title of the group
 * @param {string} props.description - Optional description for the group
 * @param {boolean} props.collapsible - Whether the group can be collapsed
 * @param {boolean} props.defaultCollapsed - Whether the group is collapsed by default
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.divider - Whether to show a divider above the group
 */
export function FormGroup({
  children,
  title,
  description,
  className,
  divider = false,
}) {
  return (
    <div className={cn(
      "space-y-4 p-4 rounded-lg border border-border shadow-sm",
      divider && "mt-6",
      className
    )}>
      {title && (
        <div className="space-y-1 pb-2 border-b mb-4">
          <h3 className="text-lg font-medium">{title}</h3>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
}
