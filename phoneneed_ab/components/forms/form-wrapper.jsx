"use client";

import { useActionState } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ActionStatus, ActionFeedback } from "@/components/feedback/action-feedback";
import { useRouter } from "next/navigation";

/**
 * Wrapper component for forms that use server actions
 * Provides consistent layout and handles action state
 */
export function FormWrapper({
  title,
  description,
  action,
  defaultState,
  successRedirect,
  children,
  submitLabel = "Save",
  cancelHref,
  cancelLabel = "Cancel",
  isSubmitting,
  className = "",
}) {
  const router = useRouter();
  const [state, formAction] = useActionState(action, defaultState);

  // Handle redirect after successful submission
  if (state?.status === "success" && successRedirect) {
    router.push(successRedirect);
  }

  return (
    <Card className={className}>
      <form action={formAction}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent className="space-y-4">
          {children}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div>
            <ActionStatus state={state} />
          </div>
          <div className="flex gap-2">
            {cancelHref && (
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(cancelHref)}
              >
                {cancelLabel}
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting || state?.status === "pending"}>
              {state?.status === "pending" ? "Saving..." : submitLabel}
            </Button>
          </div>
        </CardFooter>
      </form>
      <ActionFeedback state={state} />
    </Card>
  );
}
