"use client";

import { useState, useRef, useTransition } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Upload, X, Image as ImageIcon } from "lucide-react";
import Image from "next/image";
import { uploadImage } from "@/actions";
import { ActionStatus } from "@/components/feedback";

/**
 * Image upload component with preview and URL input fallback
 */
export function ImageUpload({
  value,
  onChange,
  className = "",
  label = "Image",
  description = "Upload an image or provide a URL",
  placeholder = "https://example.com/image.jpg",
  uploadPath = "uploads",
  maxSize = 5 * 1024 * 1024, // 5MB
}) {
  const [isPending, startTransition] = useTransition();
  const [uploadState, setUploadState] = useState(null);
  const fileInputRef = useRef(null);

  // Handle file selection
  const handleFileChange = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size
    if (file.size > maxSize) {
      alert(`File size exceeds the maximum allowed size (${maxSize / (1024 * 1024)}MB)`);
      return;
    }

    // Check file type
    if (!file.type.startsWith('image/')) {
      alert('Only image files are allowed');
      return;
    }

    // Create FormData
    const formData = new FormData();
    formData.append('file', file);
    formData.append('path', uploadPath);

    // Use startTransition to handle the upload
    startTransition(async () => {
      try {
        // Upload the image
        const result = await uploadImage(formData);

        // Update state with result
        setUploadState(result);

        // If successful, update the value
        if (result?.url) {
          onChange(result.url);
        }
      } catch (error) {
        console.error('Error uploading image:', error);
        setUploadState({
          status: 'error',
          message: error.message || 'Failed to upload image'
        });
      }
    });
  };

  // Handle URL input change
  const handleUrlChange = (e) => {
    const url = e.target.value;

    // If the URL is empty, clear the value
    if (!url.trim()) {
      onChange('');
      return;
    }

    // Always update the field to allow the user to continue typing
    // The form validation will catch any errors
    onChange(url);
  };

  // Helper function to validate URLs and paths
  const isValidUrl = (string) => {
    if (!string || typeof string !== 'string') {
      return false;
    }

    // Check if it's a relative path starting with /
    if (string.startsWith('/')) {
      // Simple validation for relative paths
      return /^\/[a-zA-Z0-9_\-\/\.]+$/.test(string);
    }

    // Check if it's a valid URL
    if (string.startsWith('http://') || string.startsWith('https://')) {
      try {
        new URL(string);
        return true;
      } catch (_) {
        return false;
      }
    }

    // Accept any non-empty string as a valid path
    return string.trim().length > 0;
  };

  // Handle clear image
  const handleClearImage = () => {
    onChange('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Image preview */}
      {value && (
        <div className="relative w-full h-40 rounded-md overflow-hidden border">
          {/* Check if value is a valid URL or path before rendering Image */}
          {isValidUrl(value) ? (
            <Image
              src={value}
              alt={label}
              fill
              className="object-contain"
              unoptimized={value.startsWith('/')} // Don't optimize local images
            />
          ) : (
            <div className="flex items-center justify-center h-full bg-muted">
              <ImageIcon className="h-10 w-10 text-muted-foreground" />
            </div>
          )}
          <Button
            type="button"
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2"
            onClick={handleClearImage}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Remove image</span>
          </Button>
        </div>
      )}

      {/* URL input */}
      <div className="flex gap-2">
        <div className="flex-1">
          <Input
            type="text"
            placeholder={placeholder}
            value={value || ''}
            onChange={handleUrlChange}
            disabled={isPending}
          />
        </div>

        {/* Upload button */}
        <div>
          <Input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            className="hidden"
            onChange={handleFileChange}
            disabled={isPending}
          />
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isPending}
          >
            {isPending ? (
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            ) : (
              <Upload className="h-4 w-4 mr-2" />
            )}
            Upload
          </Button>
        </div>
      </div>

      {/* Upload status */}
      <ActionStatus state={uploadState} />

      {/* Description */}
      <p className="text-sm text-muted-foreground">
        {description}
      </p>
    </div>
  );
}
