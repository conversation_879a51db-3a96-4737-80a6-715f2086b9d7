"use client";

import { useEffect } from "react";
import { CheckCircle, AlertCircle, XCircle } from "lucide-react";
import { toast } from "sonner";

/**
 * Component that shows toast notifications based on action state
 * Used for server actions with useActionState
 */
export function ActionFeedback({ state, resetState }) {
  useEffect(() => {
    if (state?.status === "success") {
      toast.success(state.message || "Operation completed successfully");
      if (resetState) {
        resetState();
      }
    } else if (state?.status === "error") {
      toast.error(state.message || "An error occurred");
    }
  }, [state, resetState]);

  if (!state) return null;

  return null;
}

/**
 * Component that shows inline status messages based on action state
 * Used for server actions with useActionState
 */
export function ActionStatus({ state, className = "" }) {
  if (!state) return null;

  if (state.status === "pending") {
    return (
      <div className={`flex items-center text-muted-foreground ${className}`}>
        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
        <span>Processing...</span>
      </div>
    );
  }

  if (state.status === "success") {
    return (
      <div className={`flex items-center text-primary ${className}`}>
        <CheckCircle className="mr-2 h-4 w-4" />
        <span>{state.message || "Success"}</span>
      </div>
    );
  }

  if (state.status === "error") {
    return (
      <div className={`flex items-center text-destructive ${className}`}>
        <XCircle className="mr-2 h-4 w-4" />
        <span>{state.message || "Error"}</span>
      </div>
    );
  }

  return null;
}
