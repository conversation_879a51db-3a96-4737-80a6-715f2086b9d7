"use client";

import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { ActionStatus } from "@/components/feedback/action-feedback";

/**
 * Component for confirmation dialogs with action state handling
 */
export function ConfirmDialog({
  title = "Are you sure?",
  description = "This action cannot be undone.",
  cancelText = "Cancel",
  confirmText = "Confirm",
  variant = "destructive",
  action,
  state,
  children,
  disabled = false,
}) {
  const [open, setOpen] = useState(false);

  const handleAction = async () => {
    if (action) {
      await action();
      if (state?.status !== "error") {
        setOpen(false);
      }
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        {children}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex items-center">
          <div className="mr-auto">
            {state && <ActionStatus state={state} />}
          </div>
          <AlertDialogCancel disabled={state?.status === "pending"}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={(e) => {
              e.preventDefault();
              handleAction();
            }}
            disabled={disabled || state?.status === "pending"}
            className={`bg-${variant} hover:bg-${variant}/90`}
          >
            {state?.status === "pending" ? "Processing..." : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
