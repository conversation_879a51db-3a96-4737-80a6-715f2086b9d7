"use client";

import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";

/**
 * A brand filter component for filtering by brand
 *
 * @param {Object} props
 * @param {Array} props.brands - List of brands to select from
 * @param {string} props.value - Currently selected brand ID
 * @param {Function} props.onChange - Function to call when selection changes
 * @param {string} props.name - Name of the filter (for form submission)
 * @param {string} props.label - Label to display above the select
 */
export function BrandFilter({
  brands = [],
  value = "",
  onChange,
  name = "brand_id",
  label = "Brand"
}) {
  const handleChange = (newValue) => {
    if (onChange) {
      // Convert "all" to empty string to clear the filter
      const finalValue = newValue === "all" ? "" : newValue;
      onChange(finalValue);
    }
  };

  return (
    <div className="space-y-2">
      <Label htmlFor={name}>{label}</Label>
      <Select
        value={value === "" ? "all" : value}
        onValueChange={handleChange}
        name={name}
      >
        <SelectTrigger id={name}>
          <SelectValue placeholder="All Brands" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all" index={0}>All Brands</SelectItem>
          {brands.map((brand, index) => (
            <SelectItem
              key={brand._id}
              value={brand._id}
              index={index + 1} // +1 to account for "All Brands" item
            >
              {brand.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
