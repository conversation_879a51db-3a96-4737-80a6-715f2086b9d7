"use client";

import React, { useState, useEffect, useTransition } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { X, Filter, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

/**
 * A reusable filter section component that manages filter state and URL parameters
 *
 * @param {Object} props
 * @param {React.ReactNode} props.children - Filter form elements
 * @param {Object} props.defaultValues - Default values for the filters
 * @param {boolean} props.showReset - Whether to show the reset button (default: true)
 */
export function FilterSection({
  children,
  defaultValues = {},
  showReset = true
}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // For transitions
  const [isPending, startTransition] = useTransition();

  // Initialize filter values from URL search params or default values
  const [filterValues, setFilterValues] = useState(() => {
    const initialValues = { ...defaultValues };

    // Populate from URL search params if available
    for (const [key, value] of searchParams.entries()) {
      if (key in initialValues || key === 'page') {
        initialValues[key] = value;
      }
    }

    return initialValues;
  });

  // Update filter values when search params change (e.g., browser back button)
  useEffect(() => {
    const newValues = { ...defaultValues };

    for (const [key, value] of searchParams.entries()) {
      if (key in newValues || key === 'page') {
        newValues[key] = value;
      }
    }

    setFilterValues(newValues);
  }, [searchParams, defaultValues]);

  // Handle filter change
  const handleFilterChange = (key, value) => {
    setFilterValues(prev => ({
      ...prev,
      [key]: value,
      // Reset page when filters change
      page: '1'
    }));
  };

  // Apply filters to URL
  const applyFilters = () => {
    startTransition(() => {
      const params = new URLSearchParams();

      // Add all non-empty filter values to URL
      Object.entries(filterValues).forEach(([key, value]) => {
        // Skip empty values, but include "all" for select filters
        if (value !== undefined && value !== null && value !== '') {
          // Don't add "all" values to the URL as they represent no filter
          if (value !== 'all') {
            params.set(key, value.toString());
          }
        }
      });

      const url = `${pathname}?${params.toString()}`;

      // Navigate to the new URL with filters
      router.push(url);

      // Force a refresh to ensure the server component re-renders with the new filter
      router.refresh();
    });
  };

  // Reset all filters
  const resetFilters = () => {
    startTransition(() => {
      // Reset to default values
      setFilterValues({ ...defaultValues });

      // Navigate to the base URL without filters
      router.push(pathname);

      // Force a refresh to ensure the server component re-renders
      router.refresh();
    });
  };

  // Process children with additional props
  const renderChildren = () => {
    if (!children) return null;

    // Handle single child
    if (!Array.isArray(children) && React.isValidElement(children)) {
      return React.cloneElement(children, {
        value: filterValues[children.props.name] || '',
        onChange: (value) => handleFilterChange(children.props.name, value),
      });
    }

    // Handle array of children
    return React.Children.map(children, child => {
      if (React.isValidElement(child)) {
        return React.cloneElement(child, {
          value: filterValues[child.props.name] || '',
          onChange: (value) => handleFilterChange(child.props.name, value),
        });
      }
      return child;
    });
  };

  return (
    <Card className="mb-6 transition-all duration-300">
      <CardContent className={cn(
        "pt-6 transition-all duration-300",
        isPending && "opacity-70"
      )}>
        <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
          <div className="flex-1 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {renderChildren()}
          </div>

          <div className="flex items-end space-x-2">
            <Button
              onClick={applyFilters}
              className="flex-shrink-0"
              disabled={isPending}
            >
              {isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Applying...
                </>
              ) : (
                'Apply Filters'
              )}
            </Button>

            {showReset && (
              <Button
                variant="outline"
                onClick={resetFilters}
                className="flex-shrink-0"
                disabled={isPending}
              >
                <X className="h-4 w-4 mr-2" />
                Reset
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
