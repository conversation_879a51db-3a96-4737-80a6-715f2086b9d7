"use client";

import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";

/**
 * A series filter component for filtering by series
 *
 * @param {Object} props
 * @param {Array} props.series - List of series to select from
 * @param {string} props.value - Currently selected series ID
 * @param {Function} props.onChange - Function to call when selection changes
 * @param {string} props.name - Name of the filter (for form submission)
 * @param {string} props.label - Label to display above the select
 */
export function SeriesFilter({
  series = [],
  value = "",
  onChange,
  name = "series_id",
  label = "Series"
}) {
  const handleChange = (newValue) => {
    if (onChange) {
      // Convert "all" to empty string to clear the filter
      const finalValue = newValue === "all" ? "" : newValue;
      onChange(finalValue);
    }
  };

  return (
    <div className="space-y-2">
      <Label htmlFor={name}>{label}</Label>
      <Select
        value={value === "" ? "all" : value}
        onValueChange={handleChange}
        name={name}
      >
        <SelectTrigger id={name}>
          <SelectValue placeholder="All Series" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all" index={0}>All Series</SelectItem>
          {series.map((seriesItem, index) => (
            <SelectItem
              key={seriesItem._id}
              value={seriesItem._id}
              index={index + 1} // +1 to account for "All Series" item
            >
              {seriesItem.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
