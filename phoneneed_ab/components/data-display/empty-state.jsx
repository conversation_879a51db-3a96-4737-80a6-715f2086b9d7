"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Plus } from "lucide-react";

/**
 * Component for displaying empty states with optional action button
 */
export function EmptyState({
  title = "No data found",
  description = "Get started by creating your first item.",
  icon: Icon,
  actionHref,
  actionLabel = "Create",
  children,
}) {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center rounded-lg border border-dashed">
      {Icon && <Icon className="h-12 w-12 text-muted-foreground mb-4" />}
      <h3 className="text-lg font-medium">{title}</h3>
      <p className="text-sm text-muted-foreground mt-1 mb-4 max-w-md">
        {description}
      </p>
      {actionHref && (
        <Button asChild>
          <Link href={actionHref}>
            <Plus className="mr-2 h-4 w-4" />
            {actionLabel}
          </Link>
        </Button>
      )}
      {children}
    </div>
  );
}
