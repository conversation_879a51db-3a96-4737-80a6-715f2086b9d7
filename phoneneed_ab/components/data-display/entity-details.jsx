"use client";

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Pencil, Trash2 } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

/**
 * Component for displaying entity details in a consistent format
 */
export function EntityDetails({
  title,
  description,
  entity,
  fields,
  items, // For backward compatibility
  editHref,
  onDelete,
  imageField,
  children,
}) {
  // Use fields if provided, otherwise use items
  const displayFields = fields || items || [];
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          <div className="flex gap-2">
            {editHref && (
              <Button variant="outline" size="sm" asChild>
                <Link href={editHref}>
                  <Pencil className="h-4 w-4 mr-2" />
                  Edit
                </Link>
              </Button>
            )}
            {onDelete && (
              <Button variant="destructive" size="sm" onClick={onDelete}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {imageField && entity[imageField] && (
          <div className="flex justify-center">
            <div className="relative w-40 h-40 rounded-md overflow-hidden">
              <Image
                src={entity[imageField]}
                alt={entity.name || "Image"}
                fill
                className="object-cover"
              />
            </div>
          </div>
        )}

        <div className="grid gap-4 md:grid-cols-2">
          {displayFields.map((field) => {
            // Handle both field formats (key/label and label/value)
            const fieldKey = field.key || 'value';
            const fieldLabel = field.label;
            const fieldValue = field.key ? entity[field.key] : field.value;

            return (
              <div key={fieldKey + fieldLabel} className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  {fieldLabel}
                </p>
                <p className="text-sm">
                  {field.format
                    ? field.format(fieldValue)
                    : fieldValue === undefined || fieldValue === null
                    ? "—"
                    : typeof fieldValue === "boolean"
                    ? fieldValue ? "Yes" : "No"
                    : fieldValue}
                </p>
              </div>
            );
          })}
        </div>

        {children}
      </CardContent>
    </Card>
  );
}
