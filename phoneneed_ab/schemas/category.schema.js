import { z } from 'zod';

// Base schema for category fields
const categoryBaseSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be 100 characters or less'),
  slug: z.string().min(1, 'Slug is required').max(100, 'Slug must be 100 characters or less')
    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  parent_id: z.union([
    z.string(),
    z.literal("none"),
    z.null()
  ]).optional(),
  is_active: z.boolean().default(true),
  is_primary: z.boolean().default(false),
  is_menu_item: z.boolean().default(false),
  position: z.number().int().positive().default(1),
  img: z.string().optional(),
  title: z.string().optional(),
  description: z.string().optional(),
  seo_title: z.string().optional(),
  seo_description: z.string().optional(),
});

// Schema for creating a new category
export const createCategorySchema = categoryBaseSchema;

// Schema for updating an existing category
export const updateCategorySchema = categoryBaseSchema;

// Schema for filtering categories
export const categoryFilterSchema = z.object({
  name: z.string().optional(),
  parent_id: z.union([
    z.string(),
    z.literal("none"),
    z.null()
  ]).optional(),
  is_active: z.boolean().optional(),
  is_primary: z.boolean().optional(),
  is_menu_item: z.boolean().optional(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().default(10),
  sort: z.string().default('position'),
  order: z.enum(['asc', 'desc']).default('asc'),
});
