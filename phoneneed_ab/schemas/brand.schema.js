import { z } from 'zod';

// Base schema for brand validation
export const brandSchema = z.object({
  name: z.string().min(1, 'Brand name is required'),
  slug: z.string().min(1, 'Slug is required'),
  is_active: z.boolean().default(true),
  img: z.string().optional(),
  position: z.number().int().positive().default(1),
  external_data: z.object({
    source: z.enum(['gsmarena', 'manual']).default('manual'),
    external_id: z.string().optional(),
    last_synced: z.date().nullable().optional(),
  }).optional().default({
    source: 'manual',
    external_id: '',
    last_synced: null,
  }),
});

// Schema for creating a new brand
export const createBrandSchema = brandSchema.omit({
  // Fields that are generated on the server
});

// Schema for updating an existing brand
export const updateBrandSchema = brandSchema.partial().extend({
  _id: z.string().optional(),
});

// Schema for filtering brands
export const brandFilterSchema = z.object({
  name: z.string().optional(),
  is_active: z.boolean().optional(),
  sort: z.enum(['name', 'position', 'created_at']).optional().default('position'),
  order: z.enum(['asc', 'desc']).optional().default('asc'),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().optional().default(10),
});
