import { z } from 'zod';

// Base schema for series validation
export const seriesSchema = z.object({
  name: z.string().min(1, 'Series name is required'),
  slug: z.string().min(1, 'Slug is required'),
  brand_id: z.string().min(1, 'Brand ID is required'),
  is_active: z.boolean().default(true),
  img: z.string().optional().or(z.literal('')),
  position: z.number().int().positive().default(1),
});

// Schema for creating a new series
export const createSeriesSchema = seriesSchema.omit({
  // Fields that are generated on the server
});

// Schema for updating an existing series
export const updateSeriesSchema = seriesSchema.partial().extend({
  _id: z.string().optional(),
});

// Schema for filtering series
export const seriesFilterSchema = z.object({
  name: z.string().optional(),
  brand_id: z.string().optional().transform(val => val || ''),
  is_active: z.boolean().optional(),
  sort: z.enum(['name', 'position', 'created_at']).optional().default('position'),
  order: z.enum(['asc', 'desc']).optional().default('asc'),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().optional().default(10),
});
