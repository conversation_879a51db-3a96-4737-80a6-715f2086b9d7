import { z } from 'zod';

// Base schema for device validation
export const deviceSchema = z.object({
  name: z.string().min(1, 'Device name is required'),
  slug: z.string().min(1, 'Slug is required'),
  brand_id: z.string().min(1, 'Brand ID is required'),
  series_id: z.union([
    z.string().min(1, 'Series ID is required'),
    z.literal('none'),
    z.null()
  ]).optional(),
  is_active: z.boolean().default(true),
  img: z.string().optional().or(z.literal('')),
  position: z.number().int().positive().default(1),
});

// Schema for creating a new device
export const createDeviceSchema = deviceSchema.omit({
  // Fields that are generated on the server
});

// Schema for updating an existing device
export const updateDeviceSchema = deviceSchema.partial().extend({
  _id: z.string().optional(),
});

// Schema for filtering devices
export const deviceFilterSchema = z.object({
  name: z.string().optional(),
  brand_id: z.string().optional(),
  series_id: z.string().optional(),
  is_active: z.boolean().optional(),
  sort: z.enum(['name', 'position', 'created_at']).optional().default('position'),
  order: z.enum(['asc', 'desc']).optional().default('asc'),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().optional().default(10),
});
