import { Home, Smartphone, Layers, Tag, Settings, User, FolderTree } from "lucide-react";

/**
 * Main navigation items for the dashboard
 */
export const mainNavItems = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: Home,
    description: "Overview of your account and activity"
  }
];

/**
 * Product-related navigation items
 */
export const productNavItems = [
  {
    title: "Brands",
    href: "/dashboard/brands",
    icon: Tag,
    description: "Manage phone brands"
  },
  {
    title: "Series",
    href: "/dashboard/series",
    icon: Layers,
    description: "Manage phone series"
  },
  {
    title: "Devices",
    href: "/dashboard/devices",
    icon: Smartphone,
    description: "Manage phone devices"
  },
  {
    title: "Categories",
    href: "/dashboard/categories",
    icon: FolderTree,
    description: "Manage product categories"
  }
];

/**
 * Account-related navigation items
 */
export const accountNavItems = [
  {
    title: "Settings",
    href: "/dashboard/settings",
    icon: Settings,
    description: "Manage your account settings"
  },
  {
    title: "Profile",
    href: "/dashboard/profile",
    icon: User,
    description: "View and edit your profile"
  }
];

/**
 * Admin-only navigation items
 * Currently empty as per requirements
 */
export const adminNavItems = [];
